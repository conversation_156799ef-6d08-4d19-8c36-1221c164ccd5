# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: carta-aloa-front
  labels:
    app: carta-aloa-front
spec:
  replicas: 1
  selector:
    matchLabels:
      app: carta-aloa-front
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: carta-aloa-front
    spec:
      imagePullSecrets:
        - name: harbor-secret
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 50
              preference:
                matchExpressions:
                  - key: performance
                    operator: NotIn
                    values:
                      - slow
      containers:
        - name: carta-aloa-front
          image: harbor.logisteq.com/carta/aloa-frontend:20250430-143114
          imagePullPolicy: IfNotPresent
          ports:
          - containerPort: 3000
          env:
          - name: HOST
            value: "0.0.0.0"
          - name: NUXT_HOST
            value: "0.0.0.0"
          - name: NUXT_PORT
            value: "3000"