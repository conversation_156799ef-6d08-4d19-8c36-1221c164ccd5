apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: carta-aloa-front
  labels:
    app: carta-aloa-front
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - aloa-new-dev.cartamobility.com
    - hosts:
        - aloa-new-dev.logisteq.com
      secretName: logisteq-secret-old

  rules:
    - host: aloa-new-dev.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: carta-aloa-front
                port:
                  number: 3000

    - host: aloa-new-dev.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: carta-aloa-front
                port:
                  number: 3000
