# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fms-dev
  labels:
    app: fms-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: fms-dev
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: fms-dev
    spec:
      imagePullSecrets:
        - name: harbor-secret
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 50
            preference:
              matchExpressions:
              - key: performance
                operator: NotIn
                values:
                - slow
      containers:
        - name: fms-dev
          image: harbor.logisteq.com/carta/fms/fms-backend:20250418-114158
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "250m"

          # livenessProbe:
          #   httpGet:
          #     path: /monitor/health/liveness
          #     port: 8080
          #   initialDelaySeconds: 100
          #   periodSeconds: 30
          #   successThreshold: 1
          #   failureThreshold: 3
          #   timeoutSeconds: 10
          # readinessProbe:
          #   httpGet:
          #     path: /monitor/health/readiness
          #     port: 8080
          #   initialDelaySeconds: 120
          #   periodSeconds: 20
          #   successThreshold: 1
          #   failureThreshold: 1
          #   timeoutSeconds: 10
          envFrom:
            - configMapRef:
                name: fms-dev
          volumeMounts:
            - name: log-volume
              mountPath: /applog
      volumes:
        - name: log-volume
          hostPath:
            path: /logs/fms-dev
            type: DirectoryOrCreate
