apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fms-dev
  labels:
    app: fms-dev
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - fms-dev-api.cartamobility.com
    - hosts:
        - fms-dev-api.logisteq.com
      secretName: logisteq-secret-old

  rules:
    - host: fms-dev-api.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: fms-dev
                port:
                  number: 8080
    - host: fms-dev-api.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: fms-dev
                port:
                  number: 8080
