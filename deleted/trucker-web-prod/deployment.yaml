# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trucker-web-prod
  labels:
    app: trucker-web-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: trucker-web-prod
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: trucker-web-prod
    spec:
      imagePullSecrets:
        - name: harbor-secret
      containers:
        - name: trucker-web-prod
          image: harbor.logisteq.com/trucker/prod/trucker-web:20250415-190112
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "250m"

          envFrom:
            - configMapRef:
                name: trucker-web-prod
          ports:
            - containerPort: 3000
