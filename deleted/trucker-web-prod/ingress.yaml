apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: trucker-web-prod
  labels:
    app: trucker-web-prod
    environment: prod
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - trucker-web.cartamobility.com
    - hosts:
        - trucker-web.logisteq.com
      secretName: logisteq-secret-old

  rules:
    - host: trucker-web.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: trucker-web-prod
                port:
                  number: 3000
    - host: trucker-web.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: trucker-web-prod
                port:
                  number: 3000
