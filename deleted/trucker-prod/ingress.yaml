apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fms-service
  labels:
    app: fms-service
    environment: prod
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - fms.cartamobility.com
    - hosts:
        - fms.logisteq.com
      secretName: logisteq-secret-old

  rules:
    - host: fms.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: fms-service
                port:
                  number: 8080
    - host: fms.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: fms-service
                port:
                  number: 8080
