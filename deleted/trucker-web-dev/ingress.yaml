apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: trucker-web-dev
  labels:
    app: trucker-web-dev
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - trucker-web-dev.cartamobility.com
    - hosts:
        - trucker-web-dev.logisteq.com
      logisteq-secret-old: logisteq-secret-old

  rules:
    - host: trucker-web-dev.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: trucker-web-dev
                port:
                  number: 3000
    - host: trucker-web-dev.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: trucker-web-dev
                port:
                  number: 3000
