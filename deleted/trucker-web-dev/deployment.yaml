# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trucker-web-dev
  labels:
    app: trucker-web-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: trucker-web-dev
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: trucker-web-dev
    spec:
      imagePullSecrets:
        - name: harbor-secret
      containers:
        - name: trucker-web-dev
          image: harbor.logisteq.com/trucker/dev/trucker-web:20250415-184434
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "250m"

          envFrom:
            - configMapRef:
                name: trucker-web-dev
          ports:
            - containerPort: 3000
