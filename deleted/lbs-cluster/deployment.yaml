apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: lbs-cluster
    kind: lbs-core
    environment: dev
  name: lbs-cluster
spec:  
  replicas: 1
  selector:
    matchLabels:
      app: lbs-cluster
  strategy:
    type: Recreate

  template:
    metadata:
      labels:
        app: lbs-cluster
        kind: lbs-core
        environment: dev
    spec:
      imagePullSecrets:
      - name: harbor-secret
      containers:
      - env:
        - name: TZ
          value: Asia/Seoul
        name: lbs-cluster
        image: harbor.logisteq.com/lbs-core/cluster:latest
        imagePullPolicy: IfNotPresent

        ports:
        - containerPort: 15004
        volumeMounts:
        - mountPath: /app/lbs-core-cluster/logs
          name: host-log-path

      volumes:
      - name: host-log-path
        hostPath:
          path: /logs/cluster

