apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: carta-aloa-backend
  name: carta-aloa-backend
spec:  
  replicas: 1
  selector:
    matchLabels:
      app: carta-aloa-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

  template:
    metadata:
      labels:
        app: carta-aloa-backend
    spec:
      imagePullSecrets:
      - name: harbor-secret
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 50
              preference:
                matchExpressions:
                  - key: performance
                    operator: NotIn
                    values:
                      - slow
      containers:
      - envFrom:
        - configMapRef:
            name: carta-aloa-backend
        name: carta-aloa-backend
        image: harbor.logisteq.com/carta/aloa-api:20250428-214318
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
        volumeMounts:
        - mountPath: /logs/
          name: host-log-path

        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 180
          periodSeconds: 120

        resources:
          limits:
            cpu: 2000m
            memory: 5000Mi
          requests:
            cpu: 300m
            memory: 500Mi


      volumes:
      - name: host-log-path
        hostPath:
          path: /logs/

