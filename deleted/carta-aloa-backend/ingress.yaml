apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: carta-aloa-backend
  labels:
    app: carta-aloa-backend
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - aloa-new-dev-api.cartamobility.com

  rules:
    - host: aloa-new-dev-api.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: carta-aloa-backend
                port:
                  number: 8080
