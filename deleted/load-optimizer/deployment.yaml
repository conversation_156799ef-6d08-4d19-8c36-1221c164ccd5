apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: load-optimize
  name: load-optimize
spec:
  replicas: 1
  selector:
    matchLabels:
      app: load-optimize
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: load-optimize
    spec:
      imagePullSecrets:
      - name: harbor-secret
      containers:
      - image: harbor.logisteq.com/lbs-core/load-optimize:20250213-192801
        name: load-optimize
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3000
        resources:
          limits:
            memory: "512Mi"
            cpu: "500m"
          requests:
            memory: "256Mi"
            cpu: "250m"

        livenessProbe:
          httpGet:
            path: /api/liveness
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          successThreshold: 1
          failureThreshold: 3
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/readiness
            port: 3000
          initialDelaySeconds: 20
          periodSeconds: 20
          successThreshold: 1
          failureThreshold: 1
          timeoutSeconds: 10

        envFrom:
        - configMapRef:
            name: load-optimize # Fixed indentation and structure

        volumeMounts:
        - mountPath: /logs/load-optimize
          name: load-optimize-claim
      restartPolicy: Always
      volumes:
      - name: load-optimize-claim
        hostPath:
          path: /logs/load-optimize
          type: DirectoryOrCreate
