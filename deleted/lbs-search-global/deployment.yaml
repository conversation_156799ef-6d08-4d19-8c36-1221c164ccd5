apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: lbs-search-global
  name: lbs-search-global
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lbs-search-global
  strategy:
    type: Recreate

  template:
    metadata:
      labels:
        app: lbs-search-global
        kind: lbs-core
    spec:
      nodeSelector:
        global: enable

      imagePullSecrets:
        - name: harbor-secret
      containers:
        - envFrom:
            - configMapRef:
                name: lbs-search-global
          name: lbs-search-global
          image: harbor.logisteq.com/lbs-core/global/search:20250425-174907
          imagePullPolicy: IfNotPresent

          ports:
            - containerPort: 15002
            - containerPort: 15012
          volumeMounts:
            - mountPath: /logs/debug/search
              name: host-log-path
            - mountPath: /appdata/search_core_kr/GogoRoot_KOR_TMS
              name: global-data

          livenessProbe:
            httpGet:
              path: /version
              port: 15002
            initialDelaySeconds: 30
            periodSeconds: 20
            failureThreshold: 10

          resources:
            limits:
              cpu: 2000m
              memory: 5000Mi
            requests:
              cpu: 300m
              memory: 500Mi

      volumes:
        - name: host-log-path
          hostPath:
            path: /logs/search-global
        - name: global-data
          hostPath:
            path: /appdata/globaldata
