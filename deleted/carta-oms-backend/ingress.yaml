apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: carta-oms-backend
  labels:
    app: carta-oms-backend
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - aloa-oms-dev-api.cartamobility.com
    - hosts:
        - aloa-oms-dev-api.logisteq.com
      secretName: logisteq-secret-old

  rules:
    - host: aloa-oms-dev-api.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: carta-oms-backend
                port:
                  number: 80
    - host: aloa-oms-dev-api.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: carta-oms-backend
                port:
                  number: 80
