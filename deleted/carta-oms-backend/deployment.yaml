apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: carta-oms-backend
  name: carta-oms-backend
spec:  
  replicas: 1
  selector:
    matchLabels:
      app: carta-oms-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

  template:
    metadata:
      labels:
        app: carta-oms-backend
    spec:
      imagePullSecrets:
      - name: harbor-secret
      containers:
      - envFrom:
        - configMapRef:
            name: carta-oms-backend
        name: carta-oms-backend
        image: harbor.logisteq.com/carta/backend-bslogis-dev:20250423-182725
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 80
        volumeMounts:
        - mountPath: /logs/
          name: host-log-path

        # livenessProbe:
        #   httpGet:
        #     path: /
        #     port: 80
        #   initialDelaySeconds: 180
        #   periodSeconds: 120

        resources:
          limits:
            cpu: 2000m
            memory: 5000Mi
          requests:
            cpu: 300m
            memory: 500Mi


      volumes:
      - name: host-log-path
        hostPath:
          path: /logs/

