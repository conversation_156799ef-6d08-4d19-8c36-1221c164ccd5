---
# Source: aloa/templates/lbs-proxy/configmap.yaml
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: lbs-proxy
data:
  PORT: "3003"
  API_BASE_URL:      "https://lbs-proxy.logisteq.com"
  MAP_URL: "https://map-tile2-qa.logisteq.com"
  SEARCH_SERVERS: "http://lmd2.logisteq.com:15002,http://lmd2.logisteq.com:16002,http://lmd2.logisteq.com:17002,http://aloa-core1.logisteq.com:15002,http://lbs-core-dev.logisteq.com:15002"
  ROUTE_SERVERS: "http://lmd2.logisteq.com:15003,http://lmd2.logisteq.com:16003,http://aloa-core1.logisteq.com:15001,http://aloa-core1.logisteq.com:16003,http://lbs-core-dev.logisteq.com:15003,http://lbs-core-dev.logisteq.com:16003,aloa-core2.logisteq.com:31503"
  METER_SERVERS: "http://lmd2.logisteq.com:15003,http://aloa-core1.logisteq.com:15001"
  DISPATCH_SERVERS: "http://lmd2.logisteq.com:15004,http://aloa-core1.logisteq.com:15004,http://lbs-core-dev.logisteq.com:15004"
  MAP_SERVERS: "https://map-tile2-qa.logisteq.com"
  SEARCH_WEIGHTS: "1,1,1,1,1"
  ROUTE_WEIGHTS: "1,1,1,1,3,1,1"
  METER_WEIGHTS: "1"
  DISPATCH_WEIGHTS: "1"
  MAP_WEIGHTS: "1"
