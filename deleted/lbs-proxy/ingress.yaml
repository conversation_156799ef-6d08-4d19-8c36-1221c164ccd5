apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: lbs-proxy

  labels:
    app: lbs-core
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - lbs-proxy.cartamobility.com
    - hosts:
        - lbs-proxy.logisteq.com
      secretName: logisteq-secret-old
  rules:
    - host: lbs-proxy.cartamobility.com
      http:
        paths:
          ### 검색 서버
          - path: /
            pathType: Prefix
            backend:
              service:
                name: lbs-proxy
                port:
                  number: 3003
    - host: lbs-proxy.logisteq.com
      http:
        paths:
          ### 검색 서버
          - path: /
            pathType: Prefix
            backend:
              service:
                name: lbs-proxy
                port:
                  number: 3003
