# Source: aloa/templates/lbs-proxy/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lbs-proxy
  
  labels:
    app: lbs-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lbs-proxy
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: lbs-proxy
        
    spec:
      imagePullSecrets:
      - name: harbor-secret

      containers:
      - name: lbs-proxy
        image: harbor.logisteq.com/lbs-core/lbs-proxy:20250302-151350
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3003
        envFrom:
        - configMapRef:
            name: lbs-proxy
        volumeMounts:
        - name: lbs-proxy-log
          mountPath: /applog/lbs-proxy
        resources:
          limits:
            memory: "9000Mi"
            cpu: "500m"
          requests:
            memory: "256Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /health/liveness
            port: 3003
          initialDelaySeconds: 15
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health/readiness
            port: 3003
          initialDelaySeconds: 20
          periodSeconds: 10
        lifecycle:
          preStop:
            exec:
              command: ["sleep", "30"]
      volumes:
      - name: lbs-proxy-log
        hostPath:
          path: /logs-lbs-proxy
          type: DirectoryOrCreate
