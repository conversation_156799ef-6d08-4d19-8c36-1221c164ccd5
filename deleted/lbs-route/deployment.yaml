apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: lbs-route
    kind: lbs-core
    environment: dev
  name: lbs-route
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lbs-route
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: lbs-route
        kind: lbs-core
        environment: dev
    spec:
      imagePullSecrets:
      - name: harbor-secret

      containers:
      - name: lbs-route
        image: harbor.logisteq.com/lbs-core/route:20250107-035541
        imagePullPolicy: IfNotPresent
        envFrom:
        - configMapRef:
            name: lbs-route

        ports:
        - containerPort: 15003
        - containerPort: 20304

        volumeMounts:
        - mountPath: /logs/route
          name: host-log-path

        livenessProbe:
          httpGet:
            path: /version
            port: 15003
          initialDelaySeconds: 40
          periodSeconds: 30

        resources:
          limits:
            cpu: 5000m
            memory: 8000Mi
          requests:
            cpu: 300m
            memory: 500Mi


      volumes:
      - name: host-log-path
        hostPath:
          path: /logs/route

