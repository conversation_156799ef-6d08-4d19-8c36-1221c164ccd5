# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: carta-admin-web
  labels:
    app: carta-admin-web
spec:
  replicas: 1
  selector:
    matchLabels:
      app: carta-admin-web
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: carta-admin-web
    spec:
      imagePullSecrets:
        - name: harbor-secret
      containers:
        - name: carta-admin-web
          image: harbor.logisteq.com/carta/admin-frontend:20250213-190209
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "250m"
          ports:
          - containerPort: 80
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 100
            periodSeconds: 60
            successThreshold: 1
            failureThreshold: 3
            timeoutSeconds: 10
