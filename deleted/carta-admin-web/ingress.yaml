apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: carta-admin-web
  labels:
    app: carta-admin-web
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - aloa-global.cartamobility.com

  rules:
    - host: aloa-global.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: carta-admin-web
                port:
                  number: 80
