apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: aloa-map2

  labels:
    app: aloa-map2
    kind: lbs-core
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - map-tile2-qa.cartamobility.com
    - hosts:
        - map-tile2-qa.logisteq.com
      secretName: logisteq-secret-old
  rules:
    - host: map-tile2-qa.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: aloa-map2
                port:
                  number: 18000
          - path: /amoa
            pathType: Prefix
            backend:
              service:
                name: amoa-map
                port:
                  number: 15011
    - host: map-tile2-qa.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: aloa-map2
                port:
                  number: 18000
          - path: /amoa
            pathType: Prefix
            backend:
              service:
                name: amoa-map
                port:
                  number: 15011
