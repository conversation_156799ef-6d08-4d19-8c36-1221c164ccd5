apiVersion: apps/v1
kind: Deployment
metadata:
  
  labels:
    app: aloa-map2
    kind: lbs-core
    environment: dev
  name: aloa-map2
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: aloa-map2
  template:
    metadata:
      labels:
        app: aloa-map2
        kind: lbs-core
        environment: dev
    spec:
      nodeSelector:
        apptype: lbs
      containers:
      - name: aloa-map2
        env:
        - name: TZ
          value: Asia/Seoul
        image: harbor.logisteq.com/lbs-core/dev/aloa-map2:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 15000
        volumeMounts:
        - mountPath: /appdata/webmap_kr_aloa
          name: aloa-map2-data
        - mountPath: /logs/debug/map
          name: lbs-logs
        resources:
          limits:
            cpu: 1000m
            memory: 5000Mi
          requests:
            cpu: 100m
            memory: 100Mi
      imagePullSecrets:
      - name: harbor-secret
      volumes:
      - name: aloa-map2-data
        hostPath:
          path: /appdata/webmap_aloa2
      - name: lbs-logs
        hostPath:
          path: /logs/aloa-map2