apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: lbs-vector
  name: lbs-vector
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lbs-vector
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: lbs-vector
    spec:
      nodeSelector:
        apptype: lbs
      imagePullSecrets:
      - name: harbor-secret
      containers:
      - image: harbor.logisteq.com/lbs-core/vector:latest
        name: lbs-vector
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 16000
        resources:
          limits:
            memory: "1024Mi"
            cpu: "500m"
          requests:
            memory: "256Mi"
            cpu: "100m"

        livenessProbe:
          httpGet:
            path: /version
            port: 16000
          initialDelaySeconds: 30
          periodSeconds: 30
          successThreshold: 1
          failureThreshold: 3
          timeoutSeconds: 10

        volumeMounts:
        - mountPath: /appdata/vector_data
          name: lbs-vector-claim
        - mountPath: /logs/debug/vectorservice
          name: lbs-vector-log

      restartPolicy: Always


      volumes:
      - name: lbs-vector-claim
        hostPath:
          path: /appdata/vector_data
          type: DirectoryOrCreate
      - name: lbs-vector-log
        hostPath:
          path: /logs/vectorservice
          type: DirectoryOrCreate
