apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: guideme
    kind: lbs-core
    environment: dev
  name: guideme
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: guideme
  template:
    metadata:
      labels:
        app: guideme
        kind: lbs-core
        environment: dev
    spec:
      containers:
      - name: guideme
        image: harbor.logisteq.com/lbs-core/guideme:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 16020
        resources:
          limits:
            cpu: 1000m
            memory: 5000Mi
          requests:
            cpu: 100m
            memory: 100Mi
        envFrom:
        - configMapRef:
            name: guideme
      imagePullSecrets:
      - name: harbor-secret