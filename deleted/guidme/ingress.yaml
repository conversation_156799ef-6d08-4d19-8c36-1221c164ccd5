apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: guideme

  labels:
    app: guideme
    kind: lbs-core
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - guideme.cartamobility.com
  rules:
    - host: guideme.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: guideme
                port:
                  number: 16020
