# Aloa Swagger



## swagger 편집
docs/swagger.yaml 파일을 편집한다 https://editor.swagger.io/

아래 페이지에 붙여넣기 한후 편집하고 commit 한다. 

또는 vscode 의 swagger 확장을 사용한다. (OpenAPI(Swagger) Editor)


## index.html 생성
swagger.yaml 파일편집후
generate-html.sh 파일 실행 .
실행후 생성된 index.html 을 커밋

```bash
./generate-html.sh
```

#### 참조 https://github.com/Redocly/redoc


## 배포
<EMAIL> 에 접속한후 git를 업데이트 하여 파일을 갱신한다
재구동할 필요는 없으나 index.html이 변경이 되면 재구동을 해야 적용이 된다 

```bash
cd /data-pool/kubernetes/aloa-swagger`
git pull
kubectl rollout restart deploy/lbs-swagger -n swagger
```