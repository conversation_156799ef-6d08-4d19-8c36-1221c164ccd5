apiVersion: v1
kind: ConfigMap
metadata:
  name: aloa-swagger
  namespace: swagger
data:
  URLS: '[{ url: ''./docs/swagger.yaml'', name: ''logisteq'' }]'
  URLS_PRIMARY_NAME: logisteq
  nginx.conf: |
    user  nginx;
    worker_processes  1;

    error_log  /var/log/nginx/error.log notice;
    pid        /var/run/nginx.pid;
    events {
        worker_connections  1024;
    }
    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;
        charset       utf-8;

        log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                          '$status $body_bytes_sent "$http_referer" '
                          '"$http_user_agent" "$http_x_forwarded_for"';

        access_log  /var/log/nginx/access.log  main;
        sendfile        on;
        #tcp_nopush     on;
        keepalive_timeout  65;
        #gzip  on;
        include /etc/nginx/conf.d/*.conf;
        server {
        
          location /docs/swagger.yaml {
              charset utf-8; # 해당 위치의 문자 인코딩을 UTF-8로 설정
              types {
                application/yaml yaml; # MIME 타입 설정
              }
          }


        }
    }
        