apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: aloa-swagger
  namespace: swagger
  labels:
    app: aloa-swagger
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - swagger.cartamobility.com
    - hosts:
        - swagger.logisteq.com
      secretName: logisteq-secret-old
  rules:
    - host: swagger.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: aloa-swagger
                port:
                  number: 80
    - host: swagger.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: aloa-swagger
                port:
                  number: 80
