apiVersion: apps/v1
kind: Deployment
metadata:
  name: aloa-swagger
  namespace: swagger
  labels:
    app: aloa-swagger
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aloa-swagger
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: aloa-swagger
    spec:
      nodeSelector:
        apptype: lbs
      containers:
      - envFrom:
        - configMapRef:
            name: aloa-swagger
        image: swaggerapi/swagger-ui
        name: swagger
        resources:
          limits:
            memory: "512Mi"
            cpu: "500m"
          requests:
            memory: "128Mi"
            cpu: "100m"
        ports:
        - containerPort: 8080

        volumeMounts:
        - name: swagger-yaml
          mountPath: /usr/share/nginx/html/docs
        - name: nginx-conf
          mountPath: '/etc/nginx/nginx.conf'
          subPath: 'nginx.conf'

      restartPolicy: Always

      volumes:
      - name: swagger-yaml
        hostPath:
          path: /home/<USER>/dev-k8s-cfgstore/swagger/aloa/docs
          type: DirectoryOrCreate
      - name: nginx-conf
        configMap:
          name: aloa-swagger
          items:
            - key: 'nginx.conf'
              path: 'nginx.conf'
