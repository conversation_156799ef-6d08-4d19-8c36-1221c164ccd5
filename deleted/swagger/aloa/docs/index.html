<!DOCTYPE html>
<html>

<head>
  <meta charset="utf8" />
  <title>ALOA Open API</title>
  <!-- needed for adaptive design -->
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      padding: 0;
      margin: 0;
    }
  </style>
  <script src="https://cdn.redoc.ly/redoc/v2.1.5/bundles/redoc.standalone.js"></script><style data-styled="true" data-styled-version="6.1.12">.htdgPt{width:calc(100% - 40%);padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.htdgPt{width:100%;padding:40px 40px;}}/*!sc*/
.gxopqJ{width:calc(100% - 40%);padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.gxopqJ{width:100%;padding:0px 40px;}}/*!sc*/
data-styled.g4[id="sc-fQpRED"]{content:"htdgPt,gxopqJ,"}/*!sc*/
.diwyyM{padding:40px 0;}/*!sc*/
.diwyyM:last-child{min-height:calc(100vh + 1px);}/*!sc*/
.diwyyM>.diwyyM:last-child{min-height:initial;}/*!sc*/
@media print,screen and (max-width: 75rem){.diwyyM{padding:0;}}/*!sc*/
.kcRA-dj{padding:40px 0;position:relative;}/*!sc*/
.kcRA-dj:last-child{min-height:calc(100vh + 1px);}/*!sc*/
.kcRA-dj>.kcRA-dj:last-child{min-height:initial;}/*!sc*/
@media print,screen and (max-width: 75rem){.kcRA-dj{padding:0;}}/*!sc*/
.kcRA-dj:not(:last-of-type):after{position:absolute;bottom:0;width:100%;display:block;content:'';border-bottom:1px solid rgba(0, 0, 0, 0.2);}/*!sc*/
data-styled.g5[id="sc-dsLQwm"]{content:"diwyyM,kcRA-dj,"}/*!sc*/
.fTxZsC{width:40%;color:#ffffff;background-color:#263238;padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.fTxZsC{width:100%;padding:40px 40px;}}/*!sc*/
data-styled.g6[id="sc-iKTcqh"]{content:"fTxZsC,"}/*!sc*/
.drGLlX{background-color:#263238;}/*!sc*/
data-styled.g7[id="sc-gnpbhQ"]{content:"drGLlX,"}/*!sc*/
.dSIRVR{display:flex;width:100%;padding:0;}/*!sc*/
@media print,screen and (max-width: 75rem){.dSIRVR{flex-direction:column;}}/*!sc*/
data-styled.g8[id="sc-la-DxNn"]{content:"dSIRVR,"}/*!sc*/
.gfRsnu{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#333333;}/*!sc*/
data-styled.g9[id="sc-iCZwEW"]{content:"gfRsnu,"}/*!sc*/
.cbpGTP{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;margin:0 0 20px;}/*!sc*/
data-styled.g10[id="sc-knesRu"]{content:"cbpGTP,"}/*!sc*/
.dVnbUW{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.27em;line-height:1.6em;color:#333333;}/*!sc*/
data-styled.g11[id="sc-dnaUSb"]{content:"dVnbUW,"}/*!sc*/
.jKNCPF{color:#ffffff;}/*!sc*/
data-styled.g12[id="sc-kkmypM"]{content:"jKNCPF,"}/*!sc*/
.gwrByh{border-bottom:1px solid rgba(38, 50, 56, 0.3);margin:1em 0 1em 0;color:rgba(38, 50, 56, 0.5);font-weight:normal;text-transform:uppercase;font-size:0.929em;line-height:20px;}/*!sc*/
data-styled.g13[id="sc-dkjaqt"]{content:"gwrByh,"}/*!sc*/
.hSvuOo{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.hSvuOo:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
h1:hover>.hSvuOo::before,h2:hover>.hSvuOo::before,.hSvuOo:hover::before{visibility:visible;}/*!sc*/
data-styled.g14[id="sc-jCbFiK"]{content:"hSvuOo,"}/*!sc*/
.gUrACV{height:18px;width:18px;min-width:18px;vertical-align:middle;float:right;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.dJanPw{height:1.3em;width:1.3em;min-width:1.3em;vertical-align:middle;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.fqtTpb{height:1.5em;width:1.5em;min-width:1.5em;vertical-align:middle;float:left;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.fqtTpb polygon{fill:#1d8127;}/*!sc*/
.ezFOZv{height:1.5em;width:1.5em;min-width:1.5em;vertical-align:middle;float:left;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.ezFOZv polygon{fill:#d41f1c;}/*!sc*/
.iMxoRf{height:20px;width:20px;min-width:20px;vertical-align:middle;float:right;transition:transform 0.2s ease-out;transform:rotateZ(0);}/*!sc*/
.iMxoRf polygon{fill:white;}/*!sc*/
.MHxpd{height:18px;width:18px;min-width:18px;vertical-align:middle;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
data-styled.g15[id="sc-cBYhjr"]{content:"gUrACV,dJanPw,fqtTpb,ezFOZv,iMxoRf,MHxpd,"}/*!sc*/
.gbdrVc{border-left:1px solid #7c7cbb;box-sizing:border-box;position:relative;padding:10px 10px 10px 0;}/*!sc*/
@media screen and (max-width: 50rem){.gbdrVc{display:block;overflow:hidden;}}/*!sc*/
tr:first-of-type>.gbdrVc,tr.last>.gbdrVc{border-left-width:0;background-position:top left;background-repeat:no-repeat;background-size:1px 100%;}/*!sc*/
tr:first-of-type>.gbdrVc{background-image:linear-gradient(
      to bottom,
      transparent 0%,
      transparent 22px,
      #7c7cbb 22px,
      #7c7cbb 100%
    );}/*!sc*/
tr.last>.gbdrVc{background-image:linear-gradient(
      to bottom,
      #7c7cbb 0%,
      #7c7cbb 22px,
      transparent 22px,
      transparent 100%
    );}/*!sc*/
tr.last+tr>.gbdrVc{border-left-color:transparent;}/*!sc*/
tr.last:first-child>.gbdrVc{background:none;border-left-color:transparent;}/*!sc*/
data-styled.g18[id="sc-tOkKi"]{content:"gbdrVc,"}/*!sc*/
.bUksBx{vertical-align:top;line-height:20px;white-space:nowrap;font-size:13px;font-family:Courier,monospace;}/*!sc*/
.bUksBx.deprecated{text-decoration:line-through;color:#707070;}/*!sc*/
data-styled.g20[id="sc-epPVmt"]{content:"bUksBx,"}/*!sc*/
.exGrJC{border-bottom:1px solid #9fb4be;padding:10px 0;width:75%;box-sizing:border-box;}/*!sc*/
tr.expanded .exGrJC{border-bottom:none;}/*!sc*/
@media screen and (max-width: 50rem){.exGrJC{padding:0 20px;border-bottom:none;border-left:1px solid #7c7cbb;}tr.last>.exGrJC{border-left:none;}}/*!sc*/
data-styled.g21[id="sc-fpSrms"]{content:"exGrJC,"}/*!sc*/
.hTjFRU{color:#7c7cbb;font-family:Courier,monospace;margin-right:10px;}/*!sc*/
.hTjFRU::before{content:'';display:inline-block;vertical-align:middle;width:10px;height:1px;background:#7c7cbb;}/*!sc*/
.hTjFRU::after{content:'';display:inline-block;vertical-align:middle;width:1px;background:#7c7cbb;height:7px;}/*!sc*/
data-styled.g22[id="sc-hfvVTD"]{content:"hTjFRU,"}/*!sc*/
.ceVHDP{border-collapse:separate;border-radius:3px;font-size:14px;border-spacing:0;width:100%;}/*!sc*/
.ceVHDP >tr{vertical-align:middle;}/*!sc*/
@media screen and (max-width: 50rem){.ceVHDP{display:block;}.ceVHDP >tr,.ceVHDP >tbody>tr{display:block;}}/*!sc*/
@media screen and (max-width: 50rem) and (-ms-high-contrast:none){.ceVHDP td{float:left;width:100%;}}/*!sc*/
.ceVHDP .sc-ifyrTC,.ceVHDP .sc-ifyrTC .sc-ifyrTC .sc-ifyrTC,.ceVHDP .sc-ifyrTC .sc-ifyrTC .sc-ifyrTC .sc-ifyrTC .sc-ifyrTC{margin:1em;margin-right:0;background:#fafafa;}/*!sc*/
.ceVHDP .sc-ifyrTC .sc-ifyrTC,.ceVHDP .sc-ifyrTC .sc-ifyrTC .sc-ifyrTC .sc-ifyrTC,.ceVHDP .sc-ifyrTC .sc-ifyrTC .sc-ifyrTC .sc-ifyrTC .sc-ifyrTC .sc-ifyrTC{background:#ffffff;}/*!sc*/
data-styled.g24[id="sc-dENhDJ"]{content:"ceVHDP,"}/*!sc*/
.jxQggo >ul{list-style:none;padding:0;margin:0;margin:0 -5px;}/*!sc*/
.jxQggo >ul >li{padding:5px 10px;display:inline-block;background-color:#11171a;border-bottom:1px solid rgba(0, 0, 0, 0.5);cursor:pointer;text-align:center;outline:none;color:#ccc;margin:0 5px 5px 5px;border:1px solid #07090b;border-radius:5px;min-width:60px;font-size:0.9em;font-weight:bold;}/*!sc*/
.jxQggo >ul >li.react-tabs__tab--selected{color:#333333;background:#ffffff;}/*!sc*/
.jxQggo >ul >li.react-tabs__tab--selected:focus{outline:auto;}/*!sc*/
.jxQggo >ul >li:only-child{flex:none;min-width:100px;}/*!sc*/
.jxQggo >ul >li.tab-success{color:#1d8127;}/*!sc*/
.jxQggo >ul >li.tab-redirect{color:#ffa500;}/*!sc*/
.jxQggo >ul >li.tab-info{color:#87ceeb;}/*!sc*/
.jxQggo >ul >li.tab-error{color:#d41f1c;}/*!sc*/
.jxQggo >.react-tabs__tab-panel{background:#11171a;}/*!sc*/
.jxQggo >.react-tabs__tab-panel>div,.jxQggo >.react-tabs__tab-panel>pre{padding:20px;margin:0;}/*!sc*/
.jxQggo >.react-tabs__tab-panel>div>pre{padding:0;}/*!sc*/
data-styled.g30[id="sc-bSlUec"]{content:"jxQggo,"}/*!sc*/
.fwfkcU code[class*='language-'],.fwfkcU pre[class*='language-']{text-shadow:0 -0.1em 0.2em black;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none;}/*!sc*/
@media print{.fwfkcU code[class*='language-'],.fwfkcU pre[class*='language-']{text-shadow:none;}}/*!sc*/
.fwfkcU pre[class*='language-']{padding:1em;margin:0.5em 0;overflow:auto;}/*!sc*/
.fwfkcU .token.comment,.fwfkcU .token.prolog,.fwfkcU .token.doctype,.fwfkcU .token.cdata{color:hsl(30, 20%, 50%);}/*!sc*/
.fwfkcU .token.punctuation{opacity:0.7;}/*!sc*/
.fwfkcU .namespace{opacity:0.7;}/*!sc*/
.fwfkcU .token.property,.fwfkcU .token.tag,.fwfkcU .token.number,.fwfkcU .token.constant,.fwfkcU .token.symbol{color:#4a8bb3;}/*!sc*/
.fwfkcU .token.boolean{color:#e64441;}/*!sc*/
.fwfkcU .token.selector,.fwfkcU .token.attr-name,.fwfkcU .token.string,.fwfkcU .token.char,.fwfkcU .token.builtin,.fwfkcU .token.inserted{color:#a0fbaa;}/*!sc*/
.fwfkcU .token.selector+a,.fwfkcU .token.attr-name+a,.fwfkcU .token.string+a,.fwfkcU .token.char+a,.fwfkcU .token.builtin+a,.fwfkcU .token.inserted+a,.fwfkcU .token.selector+a:visited,.fwfkcU .token.attr-name+a:visited,.fwfkcU .token.string+a:visited,.fwfkcU .token.char+a:visited,.fwfkcU .token.builtin+a:visited,.fwfkcU .token.inserted+a:visited{color:#4ed2ba;text-decoration:underline;}/*!sc*/
.fwfkcU .token.property.string{color:white;}/*!sc*/
.fwfkcU .token.operator,.fwfkcU .token.entity,.fwfkcU .token.url,.fwfkcU .token.variable{color:hsl(40, 90%, 60%);}/*!sc*/
.fwfkcU .token.atrule,.fwfkcU .token.attr-value,.fwfkcU .token.keyword{color:hsl(350, 40%, 70%);}/*!sc*/
.fwfkcU .token.regex,.fwfkcU .token.important{color:#e90;}/*!sc*/
.fwfkcU .token.important,.fwfkcU .token.bold{font-weight:bold;}/*!sc*/
.fwfkcU .token.italic{font-style:italic;}/*!sc*/
.fwfkcU .token.entity{cursor:help;}/*!sc*/
.fwfkcU .token.deleted{color:red;}/*!sc*/
data-styled.g32[id="sc-euGpHm"]{content:"fwfkcU,"}/*!sc*/
.bMXXJy{opacity:0.7;transition:opacity 0.3s ease;text-align:right;}/*!sc*/
.bMXXJy:focus-within{opacity:1;}/*!sc*/
.bMXXJy >button{background-color:transparent;border:0;color:inherit;padding:2px 10px;font-family:Roboto,sans-serif;font-size:14px;line-height:1.5em;cursor:pointer;outline:0;}/*!sc*/
.bMXXJy >button :hover,.bMXXJy >button :focus{background:rgba(255, 255, 255, 0.1);}/*!sc*/
data-styled.g33[id="sc-pFPEP"]{content:"bMXXJy,"}/*!sc*/
.bWVgjU{position:relative;}/*!sc*/
data-styled.g37[id="sc-dJDBYC"]{content:"bWVgjU,"}/*!sc*/
.fafqZb{margin-left:10px;text-transform:none;font-size:0.929em;color:black;}/*!sc*/
data-styled.g41[id="sc-dwYcXH"]{content:"fafqZb,"}/*!sc*/
.kqJXdD{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.kqJXdD p:last-child{margin-bottom:0;}/*!sc*/
.kqJXdD h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.kqJXdD h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.kqJXdD code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.kqJXdD pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.kqJXdD pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.kqJXdD pre code:before,.kqJXdD pre code:after{content:none;}/*!sc*/
.kqJXdD blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.kqJXdD img{max-width:100%;box-sizing:content-box;}/*!sc*/
.kqJXdD ul,.kqJXdD ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.kqJXdD ul ul,.kqJXdD ol ul,.kqJXdD ul ol,.kqJXdD ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.kqJXdD table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.kqJXdD table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.kqJXdD table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.kqJXdD table th,.kqJXdD table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.kqJXdD table th{text-align:left;font-weight:bold;}/*!sc*/
.kqJXdD .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.kqJXdD .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.kqJXdD h1:hover>.share-link::before,.kqJXdD h2:hover>.share-link::before,.kqJXdD .share-link:hover::before{visibility:visible;}/*!sc*/
.kqJXdD a{text-decoration:auto;color:#32329f;}/*!sc*/
.kqJXdD a:visited{color:#32329f;}/*!sc*/
.kqJXdD a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
.jYGAQp{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.jYGAQp p:last-child{margin-bottom:0;}/*!sc*/
.jYGAQp p:first-child{margin-top:0;}/*!sc*/
.jYGAQp p:last-child{margin-bottom:0;}/*!sc*/
.jYGAQp h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.jYGAQp h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.jYGAQp code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.jYGAQp pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.jYGAQp pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.jYGAQp pre code:before,.jYGAQp pre code:after{content:none;}/*!sc*/
.jYGAQp blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.jYGAQp img{max-width:100%;box-sizing:content-box;}/*!sc*/
.jYGAQp ul,.jYGAQp ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.jYGAQp ul ul,.jYGAQp ol ul,.jYGAQp ul ol,.jYGAQp ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.jYGAQp table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.jYGAQp table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.jYGAQp table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.jYGAQp table th,.jYGAQp table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.jYGAQp table th{text-align:left;font-weight:bold;}/*!sc*/
.jYGAQp .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.jYGAQp .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.jYGAQp h1:hover>.share-link::before,.jYGAQp h2:hover>.share-link::before,.jYGAQp .share-link:hover::before{visibility:visible;}/*!sc*/
.jYGAQp a{text-decoration:auto;color:#32329f;}/*!sc*/
.jYGAQp a:visited{color:#32329f;}/*!sc*/
.jYGAQp a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
data-styled.g42[id="sc-exayXG"]{content:"kqJXdD,jYGAQp,"}/*!sc*/
.dRdjww{display:inline;}/*!sc*/
data-styled.g43[id="sc-dHrNzZ"]{content:"dRdjww,"}/*!sc*/
.ecCAmX{position:relative;}/*!sc*/
data-styled.g44[id="sc-eHujzY"]{content:"ecCAmX,"}/*!sc*/
.dgzSkD:hover>.sc-pFPEP{opacity:1;}/*!sc*/
data-styled.g49[id="sc-eTdEpr"]{content:"dgzSkD,"}/*!sc*/
.bDqsIm{font-family:Courier,monospace;font-size:13px;white-space:pre;contain:content;overflow-x:auto;}/*!sc*/
.bDqsIm .redoc-json code>.collapser{display:none;pointer-events:none;}/*!sc*/
.bDqsIm .callback-function{color:gray;}/*!sc*/
.bDqsIm .collapser:after{content:'-';cursor:pointer;}/*!sc*/
.bDqsIm .collapsed>.collapser:after{content:'+';cursor:pointer;}/*!sc*/
.bDqsIm .ellipsis:after{content:' … ';}/*!sc*/
.bDqsIm .collapsible{margin-left:2em;}/*!sc*/
.bDqsIm .hoverable{padding-top:1px;padding-bottom:1px;padding-left:2px;padding-right:2px;border-radius:2px;}/*!sc*/
.bDqsIm .hovered{background-color:rgba(235, 238, 249, 1);}/*!sc*/
.bDqsIm .collapser{background-color:transparent;border:0;color:#fff;font-family:Courier,monospace;font-size:13px;padding-right:6px;padding-left:6px;padding-top:0;padding-bottom:0;display:flex;align-items:center;justify-content:center;width:15px;height:15px;position:absolute;top:4px;left:-1.5em;cursor:default;user-select:none;-webkit-user-select:none;padding:2px;}/*!sc*/
.bDqsIm .collapser:focus{outline-color:#fff;outline-style:dotted;outline-width:1px;}/*!sc*/
.bDqsIm ul{list-style-type:none;padding:0px;margin:0px 0px 0px 26px;}/*!sc*/
.bDqsIm li{position:relative;display:block;}/*!sc*/
.bDqsIm .hoverable{display:inline-block;}/*!sc*/
.bDqsIm .selected{outline-style:solid;outline-width:1px;outline-style:dotted;}/*!sc*/
.bDqsIm .collapsed>.collapsible{display:none;}/*!sc*/
.bDqsIm .ellipsis{display:none;}/*!sc*/
.bDqsIm .collapsed>.ellipsis{display:inherit;}/*!sc*/
data-styled.g50[id="sc-fTgapq"]{content:"bDqsIm,"}/*!sc*/
.KmnOC{padding:0.9em;background-color:rgba(38,50,56,0.4);margin:0 0 10px 0;display:block;font-family:Montserrat,sans-serif;font-size:0.929em;line-height:1.5em;}/*!sc*/
data-styled.g51[id="sc-ZubPq"]{content:"KmnOC,"}/*!sc*/
.dmNpjh{font-family:Montserrat,sans-serif;font-size:12px;position:absolute;z-index:1;top:-11px;left:12px;font-weight:600;color:rgba(255,255,255,0.7);}/*!sc*/
data-styled.g52[id="sc-foMnoT"]{content:"dmNpjh,"}/*!sc*/
.iLXIA-d{position:relative;}/*!sc*/
data-styled.g53[id="sc-WChnI"]{content:"iLXIA-d,"}/*!sc*/
.gDrQpr{margin-top:15px;}/*!sc*/
data-styled.g56[id="sc-AmOMz"]{content:"gDrQpr,"}/*!sc*/
.fWeVrc.deprecated span.property-name{text-decoration:line-through;color:#707070;}/*!sc*/
.fWeVrc button{background-color:transparent;border:0;outline:0;font-size:13px;font-family:Courier,monospace;cursor:pointer;padding:0;color:#333333;}/*!sc*/
.fWeVrc button:focus{font-weight:600;}/*!sc*/
.fWeVrc .sc-cBYhjr{height:1.1em;width:1.1em;}/*!sc*/
.fWeVrc .sc-cBYhjr polygon{fill:#666;}/*!sc*/
data-styled.g57[id="sc-hxyskk"]{content:"fWeVrc,"}/*!sc*/
.bJcEcT{vertical-align:middle;font-size:13px;line-height:20px;}/*!sc*/
data-styled.g58[id="sc-xuUkR"]{content:"bJcEcT,"}/*!sc*/
.kDPMlG{color:rgba(102,102,102,0.9);}/*!sc*/
data-styled.g59[id="sc-cvzDha"]{content:"kDPMlG,"}/*!sc*/
.etUsjc{color:#666;}/*!sc*/
data-styled.g60[id="sc-gKROGD"]{content:"etUsjc,"}/*!sc*/
.jljzwO{color:#666;word-break:break-word;}/*!sc*/
data-styled.g61[id="sc-grBnJl"]{content:"jljzwO,"}/*!sc*/
.gLEAmN{color:#d41f1c;font-size:0.9em;font-weight:normal;margin-left:20px;line-height:1;}/*!sc*/
data-styled.g62[id="sc-hrDJJk"]{content:"gLEAmN,"}/*!sc*/
.iWjUHw{border-radius:2px;word-break:break-word;background-color:rgba(51,51,51,0.05);color:rgba(51,51,51,0.9);padding:0 5px;border:1px solid rgba(51,51,51,0.1);font-family:Courier,monospace;}/*!sc*/
+{margin-left:0;}/*!sc*/
data-styled.g66[id="sc-cDelgQ"]{content:"iWjUHw,"}/*!sc*/
.fyveIh:after{content:' and ';font-weight:normal;}/*!sc*/
.fyveIh:last-child:after{content:none;}/*!sc*/
.fyveIh a{text-decoration:auto;color:#32329f;}/*!sc*/
.fyveIh a:visited{color:#32329f;}/*!sc*/
.fyveIh a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
data-styled.g80[id="sc-blcnQh"]{content:"fyveIh,"}/*!sc*/
.lkBZak{white-space:nowrap;}/*!sc*/
.lkBZak:after{content:' or ';white-space:pre;}/*!sc*/
.lkBZak:last-child:after,.lkBZak:only-child:after{content:none;}/*!sc*/
.lkBZak a{text-decoration:auto;color:#32329f;}/*!sc*/
.lkBZak a:visited{color:#32329f;}/*!sc*/
.lkBZak a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
data-styled.g81[id="sc-iHlJgr"]{content:"lkBZak,"}/*!sc*/
.febKCY{flex:1 1 auto;cursor:pointer;}/*!sc*/
data-styled.g82[id="sc-jHofgq"]{content:"febKCY,"}/*!sc*/
.juVMxG{width:75%;text-overflow:ellipsis;border-radius:4px;overflow:hidden;}/*!sc*/
@media screen and (max-width: 50rem){.juVMxG{margin-top:10px;}}/*!sc*/
data-styled.g83[id="sc-iRFtIJ"]{content:"juVMxG,"}/*!sc*/
.eGMvsx{display:inline-block;margin:0;}/*!sc*/
data-styled.g84[id="sc-jRHPII"]{content:"eGMvsx,"}/*!sc*/
.cCfxdS{width:100%;display:flex;margin:1em 0;flex-direction:row;}/*!sc*/
@media screen and (max-width: 50rem){.cCfxdS{flex-direction:column;}}/*!sc*/
data-styled.g85[id="sc-hWiVxH"]{content:"cCfxdS,"}/*!sc*/
.edbYXQ{margin-top:0;margin-bottom:0.5em;}/*!sc*/
data-styled.g91[id="sc-gjHHYa"]{content:"edbYXQ,"}/*!sc*/
.cwcfQE{border:1px solid #32329f;color:#32329f;font-weight:normal;margin-left:0.5em;padding:4px 8px 4px;display:inline-block;text-decoration:none;cursor:pointer;}/*!sc*/
data-styled.g92[id="sc-kyZTxD"]{content:"cwcfQE,"}/*!sc*/
.hrVgqq::before{content:'|';display:inline-block;opacity:0.5;width:15px;text-align:center;}/*!sc*/
.hrVgqq:last-child::after{display:none;}/*!sc*/
data-styled.g93[id="sc-ggPNws"]{content:"hrVgqq,"}/*!sc*/
.dgGfLY{overflow:hidden;}/*!sc*/
data-styled.g94[id="sc-kwhYVV"]{content:"dgGfLY,"}/*!sc*/
.kqOgwj{display:flex;flex-wrap:wrap;margin-left:-15px;}/*!sc*/
data-styled.g95[id="sc-fyEUpq"]{content:"kqOgwj,"}/*!sc*/
.QUyzu{width:9ex;display:inline-block;height:13px;line-height:13px;background-color:#333;border-radius:3px;background-repeat:no-repeat;background-position:6px 4px;font-size:7px;font-family:Verdana,sans-serif;color:white;text-transform:uppercase;text-align:center;font-weight:bold;vertical-align:middle;margin-right:6px;margin-top:2px;}/*!sc*/
.QUyzu.get{background-color:#2F8132;}/*!sc*/
.QUyzu.post{background-color:#186FAF;}/*!sc*/
.QUyzu.put{background-color:#95507c;}/*!sc*/
.QUyzu.options{background-color:#947014;}/*!sc*/
.QUyzu.patch{background-color:#bf581d;}/*!sc*/
.QUyzu.delete{background-color:#cc3333;}/*!sc*/
.QUyzu.basic{background-color:#707070;}/*!sc*/
.QUyzu.link{background-color:#07818F;}/*!sc*/
.QUyzu.head{background-color:#A23DAD;}/*!sc*/
.QUyzu.hook{background-color:#32329f;}/*!sc*/
.QUyzu.schema{background-color:#707070;}/*!sc*/
data-styled.g99[id="sc-exkVDC"]{content:"QUyzu,"}/*!sc*/
.hYtPsf{margin:0;padding:0;}/*!sc*/
.hYtPsf:first-child{padding-bottom:32px;}/*!sc*/
.sc-iMDhdf .sc-iMDhdf{font-size:0.929em;}/*!sc*/
.fEjWGm{margin:0;padding:0;display:none;}/*!sc*/
.fEjWGm:first-child{padding-bottom:32px;}/*!sc*/
.sc-iMDhdf .sc-iMDhdf{font-size:0.929em;}/*!sc*/
data-styled.g100[id="sc-iMDhdf"]{content:"hYtPsf,fEjWGm,"}/*!sc*/
.btrDvq{list-style:none inside none;overflow:hidden;text-overflow:ellipsis;padding:0;}/*!sc*/
data-styled.g101[id="sc-loAbOW"]{content:"btrDvq,"}/*!sc*/
.cwxKjr{cursor:pointer;color:#333333;margin:0;padding:12.5px 20px;display:flex;justify-content:space-between;font-family:Montserrat,sans-serif;font-size:0.929em;text-transform:none;background-color:#fafafa;}/*!sc*/
.cwxKjr:hover{color:#32329f;background-color:#e1e1e1;}/*!sc*/
.cwxKjr .sc-cBYhjr{height:1.5em;width:1.5em;}/*!sc*/
.cwxKjr .sc-cBYhjr polygon{fill:#333333;}/*!sc*/
.cmGsIR{cursor:pointer;color:#333333;margin:0;padding:12.5px 20px;display:flex;justify-content:space-between;font-family:Montserrat,sans-serif;background-color:#fafafa;}/*!sc*/
.cmGsIR:hover{color:#32329f;background-color:#ededed;}/*!sc*/
.cmGsIR .sc-cBYhjr{height:1.5em;width:1.5em;}/*!sc*/
.cmGsIR .sc-cBYhjr polygon{fill:#333333;}/*!sc*/
data-styled.g102[id="sc-bPZXsP"]{content:"cwxKjr,cmGsIR,"}/*!sc*/
.glMKqQ{display:inline-block;vertical-align:middle;width:calc(100% - 38px);overflow:hidden;text-overflow:ellipsis;}/*!sc*/
data-styled.g103[id="sc-eteQWc"]{content:"glMKqQ,"}/*!sc*/
.iljewH{font-size:0.8em;margin-top:10px;text-align:center;position:fixed;width:260px;bottom:0;background:#fafafa;}/*!sc*/
.iljewH a,.iljewH a:visited,.iljewH a:hover{color:#333333!important;padding:5px 0;border-top:1px solid #e1e1e1;text-decoration:none;display:flex;align-items:center;justify-content:center;}/*!sc*/
.iljewH img{width:15px;margin-right:5px;}/*!sc*/
@media screen and (max-width: 50rem){.iljewH{width:100%;}}/*!sc*/
data-styled.g104[id="sc-gkavYR"]{content:"iljewH,"}/*!sc*/
.dCbPd{cursor:pointer;position:relative;margin-bottom:5px;}/*!sc*/
data-styled.g110[id="sc-dKsqdn"]{content:"dCbPd,"}/*!sc*/
.jcAXWA{font-family:Courier,monospace;margin-left:10px;flex:1;overflow-x:hidden;text-overflow:ellipsis;}/*!sc*/
data-styled.g111[id="sc-eowDPD"]{content:"jcAXWA,"}/*!sc*/
.gsBSOU{outline:0;color:inherit;width:100%;text-align:left;cursor:pointer;padding:10px 30px 10px 20px;border-radius:4px 4px 0 0;background-color:#11171a;display:flex;white-space:nowrap;align-items:center;border:1px solid transparent;border-bottom:0;transition:border-color 0.25s ease;}/*!sc*/
.gsBSOU ..sc-eowDPD{color:#ffffff;}/*!sc*/
.gsBSOU:focus{box-shadow:inset 0 2px 2px rgba(0, 0, 0, 0.45),0 2px 0 rgba(128, 128, 128, 0.25);}/*!sc*/
data-styled.g112[id="sc-iAlELC"]{content:"gsBSOU,"}/*!sc*/
.kpMtuJ{font-size:0.929em;line-height:20px;background-color:#186FAF;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.ffmPnn{font-size:0.929em;line-height:20px;background-color:#2F8132;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
data-styled.g113[id="sc-oeqTF"]{content:"kpMtuJ,ffmPnn,"}/*!sc*/
.bFiOkX{position:absolute;width:100%;z-index:100;background:#fafafa;color:#263238;box-sizing:border-box;box-shadow:0 0 6px rgba(0, 0, 0, 0.33);overflow:hidden;border-bottom-left-radius:4px;border-bottom-right-radius:4px;transition:all 0.25s ease;visibility:hidden;transform:translateY(-50%) scaleY(0);}/*!sc*/
data-styled.g114[id="sc-ezTrPE"]{content:"bFiOkX,"}/*!sc*/
.hdRKqQ{padding:10px;}/*!sc*/
data-styled.g115[id="sc-drnuxz"]{content:"hdRKqQ,"}/*!sc*/
.jpmGrk{padding:5px;border:1px solid #ccc;background:#fff;word-break:break-all;color:#32329f;}/*!sc*/
.jpmGrk >span{color:#333333;}/*!sc*/
data-styled.g116[id="sc-hDcvty"]{content:"jpmGrk,"}/*!sc*/
.gcYfHW{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#1d8127;background-color:rgba(29,129,39,0.07);}/*!sc*/
.gcYfHW:focus{outline:auto #1d8127;}/*!sc*/
.bEasFS{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#d41f1c;background-color:rgba(212,31,28,0.07);cursor:default;}/*!sc*/
.bEasFS:focus{outline:auto #d41f1c;}/*!sc*/
.bEasFS::before{content:"—";font-weight:bold;width:1.5em;text-align:center;display:inline-block;vertical-align:top;}/*!sc*/
.bEasFS:focus{outline:0;}/*!sc*/
.gbPhWg{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#d41f1c;background-color:rgba(212,31,28,0.07);}/*!sc*/
.gbPhWg:focus{outline:auto #d41f1c;}/*!sc*/
.dSgpsc{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#1d8127;background-color:rgba(29,129,39,0.07);cursor:default;}/*!sc*/
.dSgpsc:focus{outline:auto #1d8127;}/*!sc*/
.dSgpsc::before{content:"—";font-weight:bold;width:1.5em;text-align:center;display:inline-block;vertical-align:top;}/*!sc*/
.dSgpsc:focus{outline:0;}/*!sc*/
data-styled.g119[id="sc-giOWAb"]{content:"gcYfHW,bEasFS,gbPhWg,dSgpsc,"}/*!sc*/
.gXPvFO{vertical-align:top;}/*!sc*/
data-styled.g122[id="sc-catHVh"]{content:"gXPvFO,"}/*!sc*/
.dbBFCU{font-size:1.3em;padding:0.2em 0;margin:3em 0 1.1em;color:#333333;font-weight:normal;}/*!sc*/
data-styled.g123[id="sc-eiLgtK"]{content:"dbBFCU,"}/*!sc*/
.cQxXyG{margin-bottom:30px;}/*!sc*/
data-styled.g128[id="sc-fpJhiv"]{content:"cQxXyG,"}/*!sc*/
.jQQbBk{user-select:none;width:20px;height:20px;align-self:center;display:flex;flex-direction:column;color:#32329f;}/*!sc*/
data-styled.g129[id="sc-bQEQyQ"]{content:"jQQbBk,"}/*!sc*/
.ktzGvn{width:260px;background-color:#fafafa;overflow:hidden;display:flex;flex-direction:column;backface-visibility:hidden;height:100vh;position:sticky;position:-webkit-sticky;top:0;}/*!sc*/
@media screen and (max-width: 50rem){.ktzGvn{position:fixed;z-index:20;width:100%;background:#fafafa;display:none;}}/*!sc*/
@media print{.ktzGvn{display:none;}}/*!sc*/
data-styled.g130[id="sc-dUMaFF"]{content:"ktzGvn,"}/*!sc*/
.kEKeWg{outline:none;user-select:none;background-color:#f2f2f2;color:#32329f;display:none;cursor:pointer;position:fixed;right:20px;z-index:100;border-radius:50%;box-shadow:0 0 20px rgba(0, 0, 0, 0.3);bottom:44px;width:60px;height:60px;padding:0 20px;}/*!sc*/
@media screen and (max-width: 50rem){.kEKeWg{display:flex;}}/*!sc*/
.kEKeWg svg{color:#0065FB;}/*!sc*/
@media print{.kEKeWg{display:none;}}/*!sc*/
data-styled.g131[id="sc-elEJnV"]{content:"kEKeWg,"}/*!sc*/
.fabhKa{font-family:Roboto,sans-serif;font-size:14px;font-weight:400;line-height:1.5em;color:#333333;display:flex;position:relative;text-align:left;-webkit-font-smoothing:antialiased;font-smoothing:antialiased;text-rendering:optimizeSpeed!important;tap-highlight-color:rgba(0, 0, 0, 0);text-size-adjust:100%;}/*!sc*/
.fabhKa *{box-sizing:border-box;-webkit-tap-highlight-color:rgba(255, 255, 255, 0);}/*!sc*/
data-styled.g132[id="sc-ixtKjU"]{content:"fabhKa,"}/*!sc*/
.laenHu{z-index:1;position:relative;overflow:hidden;width:calc(100% - 260px);contain:layout;}/*!sc*/
@media print,screen and (max-width: 50rem){.laenHu{width:100%;}}/*!sc*/
data-styled.g133[id="sc-dKGrn"]{content:"laenHu,"}/*!sc*/
.bWkBKa{background:#263238;position:absolute;top:0;bottom:0;right:0;width:calc((100% - 260px) * 0.4);}/*!sc*/
@media print,screen and (max-width: 75rem){.bWkBKa{display:none;}}/*!sc*/
data-styled.g134[id="sc-epzHnm"]{content:"bWkBKa,"}/*!sc*/
.dcjLWY{padding:5px 0;}/*!sc*/
data-styled.g135[id="sc-dovzVR"]{content:"dcjLWY,"}/*!sc*/
.dlxCDf{width:calc(100% - 40px);box-sizing:border-box;margin:0 20px;padding:5px 10px 5px 20px;border:0;border-bottom:1px solid #e1e1e1;font-family:Roboto,sans-serif;font-weight:bold;font-size:13px;color:#333333;background-color:transparent;outline:none;}/*!sc*/
data-styled.g136[id="sc-hAkARQ"]{content:"dlxCDf,"}/*!sc*/
.fBvPoH{position:absolute;left:20px;height:1.8em;width:0.9em;}/*!sc*/
.fBvPoH path{fill:#333333;}/*!sc*/
data-styled.g137[id="sc-kvXgyf"]{content:"fBvPoH,"}/*!sc*/
</style>
  <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet">
</head>

<body>
  
      <div id="redoc"><div class="sc-ixtKjU fabhKa redoc-wrap"><div class="sc-dUMaFF ktzGvn menu-content" style="top:0px;height:calc(100vh - 0px)"><div role="search" class="sc-dovzVR dcjLWY"><svg class="sc-kvXgyf fBvPoH search-icon" version="1.1" viewBox="0 0 1000 1000" x="0px" xmlns="http://www.w3.org/2000/svg" y="0px"><path d="M968.2,849.4L667.3,549c83.9-136.5,66.7-317.4-51.7-435.6C477.1-25,252.5-25,113.9,113.4c-138.5,138.3-138.5,362.6,0,501C219.2,730.1,413.2,743,547.6,666.5l301.9,301.4c43.6,43.6,76.9,14.9,104.2-12.4C981,928.3,1011.8,893,968.2,849.4z M524.5,522c-88.9,88.7-233,88.7-321.8,0c-88.9-88.7-88.9-232.6,0-321.3c88.9-88.7,233-88.7,321.8,0C613.4,289.4,613.4,433.3,524.5,522z"></path></svg><input placeholder="Search..." aria-label="Search" type="text" class="sc-hAkARQ dlxCDf search-input" value=""/></div><div class="sc-dJDBYC bWVgjU scrollbar-container undefined"><ul role="menu" class="sc-iMDhdf hYtPsf"><li tabindex="0" depth="1" data-item-id="section/OpenApi" role="menuitem" class="sc-loAbOW btrDvq"><label class="sc-bPZXsP cwxKjr -depth1"><span width="calc(100% - 38px)" title="알로아 고객용 OpenApi" class="sc-eteQWc glMKqQ">알로아 고객용 OpenApi</span></label></li><li tabindex="0" depth="1" data-item-id="tag/Auth" role="menuitem" class="sc-loAbOW btrDvq"><label class="sc-bPZXsP cwxKjr -depth1"><span width="calc(100% - 38px)" title="Auth" class="sc-eteQWc glMKqQ">Auth</span><svg class="sc-cBYhjr gUrACV" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-iMDhdf fEjWGm"><li tabindex="0" depth="2" data-item-id="tag/Auth/operation/auth-token-request" role="menuitem" class="sc-loAbOW btrDvq"><label class="sc-bPZXsP cmGsIR -depth2"><span type="post" class="sc-exkVDC QUyzu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eteQWc glMKqQ">억세스 토큰 발급</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/TMS" role="menuitem" class="sc-loAbOW btrDvq"><label class="sc-bPZXsP cwxKjr -depth1"><span width="calc(100% - 38px)" title="TMS" class="sc-eteQWc glMKqQ">TMS</span><svg class="sc-cBYhjr gUrACV" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-iMDhdf fEjWGm"><li tabindex="0" depth="2" data-item-id="tag/TMS/operation/tms-order-register" role="menuitem" class="sc-loAbOW btrDvq"><label class="sc-bPZXsP cmGsIR -depth2"><span type="post" class="sc-exkVDC QUyzu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eteQWc glMKqQ">배송지 주문 등록</span></label></li><li tabindex="0" depth="2" data-item-id="tag/TMS/operation/tms-order-register-with-product" role="menuitem" class="sc-loAbOW btrDvq"><label class="sc-bPZXsP cmGsIR -depth2"><span type="post" class="sc-exkVDC QUyzu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eteQWc glMKqQ">주문 등록 (상품정보 포함)</span></label></li><li tabindex="0" depth="2" data-item-id="tag/TMS/operation/tms-order-inquiry" role="menuitem" class="sc-loAbOW btrDvq"><label class="sc-bPZXsP cmGsIR -depth2"><span type="get" class="sc-exkVDC QUyzu operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eteQWc glMKqQ">배송지 주문 조회 (단건)</span></label></li><li tabindex="0" depth="2" data-item-id="tag/TMS/operation/tms-order-inquiry-multiple" role="menuitem" class="sc-loAbOW btrDvq"><label class="sc-bPZXsP cmGsIR -depth2"><span type="post" class="sc-exkVDC QUyzu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eteQWc glMKqQ">배송지 주문 조회 (다건)</span></label></li></ul></li></ul><div class="sc-gkavYR iljewH"><a target="_blank" rel="noopener noreferrer" href="https://redocly.com/redoc/">API docs by Redocly</a></div></div></div><div class="sc-elEJnV kEKeWg"><div class="sc-bQEQyQ jQQbBk"><svg class="" style="transform:translate(2px, -4px) rotate(180deg);transition:transform 0.2s ease" viewBox="0 0 926.23699 573.74994" version="1.1" x="0px" y="0px" width="15" height="15"><g transform="translate(904.92214,-879.1482)"><path d="
          m -673.67664,1221.6502 -231.2455,-231.24803 55.6165,
          -55.627 c 30.5891,-30.59485 56.1806,-55.627 56.8701,-55.627 0.6894,
          0 79.8637,78.60862 175.9427,174.68583 l 174.6892,174.6858 174.6892,
          -174.6858 c 96.079,-96.07721 175.253196,-174.68583 175.942696,
          -174.68583 0.6895,0 26.281,25.03215 56.8701,
          55.627 l 55.6165,55.627 -231.245496,231.24803 c -127.185,127.1864
          -231.5279,231.248 -231.873,231.248 -0.3451,0 -104.688,
          -104.0616 -231.873,-231.248 z
        " fill="currentColor"></path></g></svg><svg class="" style="transform:translate(2px, 4px);transition:transform 0.2s ease" viewBox="0 0 926.23699 573.74994" version="1.1" x="0px" y="0px" width="15" height="15"><g transform="translate(904.92214,-879.1482)"><path d="
          m -673.67664,1221.6502 -231.2455,-231.24803 55.6165,
          -55.627 c 30.5891,-30.59485 56.1806,-55.627 56.8701,-55.627 0.6894,
          0 79.8637,78.60862 175.9427,174.68583 l 174.6892,174.6858 174.6892,
          -174.6858 c 96.079,-96.07721 175.253196,-174.68583 175.942696,
          -174.68583 0.6895,0 26.281,25.03215 56.8701,
          55.627 l 55.6165,55.627 -231.245496,231.24803 c -127.185,127.1864
          -231.5279,231.248 -231.873,231.248 -0.3451,0 -104.688,
          -104.0616 -231.873,-231.248 z
        " fill="currentColor"></path></g></svg></div></div><div class="sc-dKGrn laenHu api-content"><div class="sc-dsLQwm diwyyM"><div class="sc-la-DxNn dSIRVR"><div class="sc-fQpRED htdgPt api-info"><h1 class="sc-iCZwEW sc-gjHHYa gfRsnu edbYXQ">ALOA Open API<!-- --> <span>(<!-- -->1.0.1<!-- -->)</span></h1><p>Download OpenAPI specification<!-- -->:<a download="openapi.json" target="_blank" class="sc-kyZTxD cwcfQE">Download</a></p><div class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"><div class="sc-kwhYVV dgGfLY"><div class="sc-fyEUpq kqOgwj"> <!-- --> <span class="sc-ggPNws hrVgqq">License:<!-- --> <a>License Logisteq</a></span> </div></div></div><div data-role="redoc-summary" html="" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"></div><div data-role="redoc-description" html="" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"></div></div></div></div><div id="section/OpenApi" data-section-id="section/OpenApi" class="sc-dsLQwm diwyyM"><div class="sc-la-DxNn dSIRVR"><div class="sc-fQpRED htdgPt"><h2 class="sc-dnaUSb dVnbUW"><a class="sc-jCbFiK hSvuOo" href="#section/OpenApi" aria-label="section/OpenApi"></a>알로아 고객용 OpenApi</h2></div></div></div><div id="tag/Auth" data-section-id="tag/Auth" class="sc-dsLQwm diwyyM"><div class="sc-la-DxNn dSIRVR"><div class="sc-fQpRED htdgPt"><h2 class="sc-knesRu cbpGTP"><a class="sc-jCbFiK hSvuOo" href="#tag/Auth" aria-label="tag/Auth"></a>Auth</h2></div></div><div class="sc-fQpRED gxopqJ"><div class="sc-euGpHm sc-exayXG fwfkcU kqJXdD redoc-markdown " html="&lt;p&gt;ALOA 접근키 발급&lt;/p&gt;
"><p>ALOA 접근키 발급</p>
</div></div></div><div id="tag/Auth/operation/auth-token-request" data-section-id="tag/Auth/operation/auth-token-request" class="sc-dsLQwm kcRA-dj"><div data-section-id="operation/auth-token-request" id="operation/auth-token-request" class="sc-la-DxNn dSIRVR"><div class="sc-fQpRED htdgPt"><h2 class="sc-knesRu cbpGTP"><a class="sc-jCbFiK hSvuOo" href="#tag/Auth/operation/auth-token-request" aria-label="tag/Auth/operation/auth-token-request"></a>억세스 토큰 발급<!-- --> </h2><div class="sc-fpJhiv cQxXyG"><div html="&lt;p&gt;고객사의 ID/Password 정보로 Auth token을 발급합니다. 기본적인 유효 기간은 3개월입니다&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"><p>고객사의 ID/Password 정보로 Auth token을 발급합니다. 기본적인 유효 기간은 3개월입니다</p>
</div></div><div class="sc-hWiVxH cCfxdS"><div class="sc-jHofgq febKCY"><h5 class="sc-dkjaqt sc-jRHPII gwrByh eGMvsx">Authorizations:</h5><svg class="sc-cBYhjr dJanPw" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-iRFtIJ juVMxG"><span class="sc-iHlJgr lkBZak"><span class="sc-blcnQh fyveIh"><i>BasicAuth</i></span></span></div></div><div><h5 class="sc-dkjaqt gwrByh">query<!-- --> Parameters</h5><table class="sc-dENhDJ ceVHDP"><tbody><tr class=""><td kind="field" title="grant_type" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">grant_type</span><div class="sc-xuUkR sc-hrDJJk bJcEcT gLEAmN">required</div></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div><div><span class="sc-xuUkR bJcEcT"> <!-- -->Default:<!-- --> </span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;client_credentials&quot;</span></div> <div><span class="sc-xuUkR bJcEcT"> <!-- -->Example:<!-- --> </span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">grant_type=client_credentials</span></div><div><div html="&lt;p&gt;Type of grant requested (client_credentials 로 요청 )&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Type of grant requested (client_credentials 로 요청 )</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="scope" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">scope</span><div class="sc-xuUkR sc-hrDJJk bJcEcT gLEAmN">required</div></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div><div><span class="sc-xuUkR bJcEcT"> <!-- -->Default:<!-- --> </span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;read write&quot;</span></div> <div><span class="sc-xuUkR bJcEcT"> <!-- -->Example:<!-- --> </span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">scope=read write</span></div><div><div html="&lt;p&gt;Scope of access requested (read write 로 요청)&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Scope of access requested (read write 로 요청)</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-eiLgtK dbBFCU">Responses</h3><div><button class="sc-giOWAb gcYfHW"><svg class="sc-cBYhjr fqtTpb" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-catHVh gXPvFO">200<!-- --> </strong><div html="&lt;p&gt;Auth Token 수신 발급 완료&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>Auth Token 수신 발급 완료</p>
</div></button></div><div><button class="sc-giOWAb bEasFS" disabled=""><strong class="sc-catHVh gXPvFO">400<!-- --> </strong><div html="&lt;p&gt;누락된 파라미터 또는 잘못된 파라미터 오류&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>누락된 파라미터 또는 잘못된 파라미터 오류</p>
</div></button></div><div><button class="sc-giOWAb gbPhWg"><svg class="sc-cBYhjr ezFOZv" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-catHVh gXPvFO">401<!-- --> </strong><div html="&lt;p&gt;인증 정보 오류&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>인증 정보 오류</p>
</div></button></div></div></div><div class="sc-iKTcqh sc-gnpbhQ fTxZsC drGLlX"><div class="sc-dKsqdn dCbPd"><button class="sc-iAlELC gsBSOU"><span type="post" class="sc-oeqTF kpMtuJ http-verb post">post</span><span class="sc-eowDPD jcAXWA">/oauth/token</span><svg class="sc-cBYhjr iMxoRf" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ezTrPE bFiOkX"><div class="sc-drnuxz hdRKqQ"><div html="&lt;p&gt;Development&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Development</p>
</div><div tabindex="0" role="button"><div class="sc-hDcvty jpmGrk"><span>https://aloa-dev.logisteq.com</span>/oauth/token</div></div></div><div class="sc-drnuxz hdRKqQ"><div html="&lt;p&gt;Stage&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Stage</p>
</div><div tabindex="0" role="button"><div class="sc-hDcvty jpmGrk"><span>https://aloa-qa.logisteq.com</span>/oauth/token</div></div></div></div></div><div><h3 class="sc-kkmypM jKNCPF"> <!-- -->Response samples<!-- --> </h3><div class="sc-bSlUec jxQggo" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R4sta:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R4sta:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R4sta:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R4sta:1" data-rttab="true">401</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R4sta:0" aria-labelledby="tab:R4sta:0"><div><div class="sc-WChnI iLXIA-d"><span class="sc-foMnoT dmNpjh">Content type</span><div class="sc-ZubPq KmnOC">application/json</div></div><div class="sc-AmOMz gDrQpr"><div class="sc-eTdEpr dgzSkD"><div class="sc-pFPEP bMXXJy"><button><div class="sc-eHujzY ecCAmX">Copy</div></button></div><div class="sc-euGpHm fwfkcU sc-fTgapq bDqsIm"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"access_token"</span>: <span class="token string">&quot;eyJhbGciOiJ...SUzI1NiI&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"token_type"</span>: <span class="token string">&quot;bearer&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"expires_in"</span>: <span class="token number">15551999</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"scope"</span>: <span class="token string">&quot;read write&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"jti"</span>: <span class="token string">&quot;cc294c6c-ff24-4aa8-8db5-aa1019d2e706&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R4sta:1" aria-labelledby="tab:R4sta:1"></div></div></div></div></div></div><div id="tag/TMS" data-section-id="tag/TMS" class="sc-dsLQwm diwyyM"><div class="sc-la-DxNn dSIRVR"><div class="sc-fQpRED htdgPt"><h2 class="sc-knesRu cbpGTP"><a class="sc-jCbFiK hSvuOo" href="#tag/TMS" aria-label="tag/TMS"></a>TMS</h2></div></div><div class="sc-fQpRED gxopqJ"><div class="sc-euGpHm sc-exayXG fwfkcU kqJXdD redoc-markdown " html="&lt;p&gt;ALOA 주문등록&lt;/p&gt;
"><p>ALOA 주문등록</p>
</div></div></div><div id="tag/TMS/operation/tms-order-register" data-section-id="tag/TMS/operation/tms-order-register" class="sc-dsLQwm kcRA-dj"><div data-section-id="operation/tms-order-register" id="operation/tms-order-register" class="sc-la-DxNn dSIRVR"><div class="sc-fQpRED htdgPt"><h2 class="sc-knesRu cbpGTP"><a class="sc-jCbFiK hSvuOo" href="#tag/TMS/operation/tms-order-register" aria-label="tag/TMS/operation/tms-order-register"></a>배송지 주문 등록<!-- --> </h2><div class="sc-fpJhiv cQxXyG"><div html="&lt;p&gt;프로젝트 정보를 포함한 배송지 주문 등록&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"><p>프로젝트 정보를 포함한 배송지 주문 등록</p>
</div></div><div class="sc-hWiVxH cCfxdS"><div class="sc-jHofgq febKCY"><h5 class="sc-dkjaqt sc-jRHPII gwrByh eGMvsx">Authorizations:</h5><svg class="sc-cBYhjr dJanPw" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-iRFtIJ juVMxG"><span class="sc-iHlJgr lkBZak"><span class="sc-blcnQh fyveIh"><i>BearerAuth</i></span></span></div></div><h5 class="sc-dkjaqt gwrByh">Request Body schema: <span class="sc-dwYcXH fafqZb">application/json</span></h5><div html="" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"></div><table class="sc-dENhDJ ceVHDP"><tbody><tr class=""><td kind="field" title="destinations" class="sc-tOkKi sc-epPVmt sc-hxyskk gbdrVc bUksBx fWeVrc"><span class="sc-hfvVTD hTjFRU"></span><button aria-label="expand destinations"><span class="property-name">destinations</span><svg class="sc-cBYhjr MHxpd" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG">Array of </span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">objects</span><span class="sc-xuUkR sc-grBnJl bJcEcT jljzwO"> (<!-- -->RequestOrderDTO<!-- -->) </span></div> <div><div html="" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"></div></div></div></td></tr><tr class=""><td kind="field" title="email" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">email</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;프로젝트의 소유자의 계정 이메일 주소.&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>프로젝트의 소유자의 계정 이메일 주소.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="projectName" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">projectName</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;생성될 프로젝트의 이름&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>생성될 프로젝트의 이름</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-eiLgtK dbBFCU">Responses</h3><div><button class="sc-giOWAb gcYfHW"><svg class="sc-cBYhjr fqtTpb" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-catHVh gXPvFO">200<!-- --> </strong><div html="&lt;p&gt;OK&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>OK</p>
</div></button></div><div><button class="sc-giOWAb gbPhWg"><svg class="sc-cBYhjr ezFOZv" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-catHVh gXPvFO">401<!-- --> </strong><div html="&lt;p&gt;Unauthorized&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>Unauthorized</p>
</div></button></div></div></div><div class="sc-iKTcqh sc-gnpbhQ fTxZsC drGLlX"><div class="sc-dKsqdn dCbPd"><button class="sc-iAlELC gsBSOU"><span type="post" class="sc-oeqTF kpMtuJ http-verb post">post</span><span class="sc-eowDPD jcAXWA">/openapi/deliveries/project</span><svg class="sc-cBYhjr iMxoRf" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ezTrPE bFiOkX"><div class="sc-drnuxz hdRKqQ"><div html="&lt;p&gt;Development&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Development</p>
</div><div tabindex="0" role="button"><div class="sc-hDcvty jpmGrk"><span>https://aloa-dev.logisteq.com</span>/openapi/deliveries/project</div></div></div><div class="sc-drnuxz hdRKqQ"><div html="&lt;p&gt;Stage&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Stage</p>
</div><div tabindex="0" role="button"><div class="sc-hDcvty jpmGrk"><span>https://aloa-qa.logisteq.com</span>/openapi/deliveries/project</div></div></div></div></div><div><h3 class="sc-kkmypM jKNCPF"> <!-- -->Request samples<!-- --> </h3><div class="sc-bSlUec jxQggo" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:Riidq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:Riidq:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:Riidq:0" aria-labelledby="tab:Riidq:0"><div><div class="sc-WChnI iLXIA-d"><span class="sc-foMnoT dmNpjh">Content type</span><div class="sc-ZubPq KmnOC">application/json</div></div><div class="sc-AmOMz gDrQpr"><div class="sc-eTdEpr dgzSkD"><div class="sc-pFPEP bMXXJy"><button><div class="sc-eHujzY ecCAmX">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-euGpHm fwfkcU sc-fTgapq bDqsIm"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"destinations"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"customerOrderId"</span>: <span class="token string">&quot;A1234567890&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"groupName"</span>: <span class="token string">&quot;강남A&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"orderAmount"</span>: <span class="token number">32000</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"productName"</span>: <span class="token string">&quot;신선사과&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"productQuantity"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"productSize"</span>: <span class="token string">&quot;대형&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"qrBarCode"</span>: <span class="token string">&quot;A1234567890&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"senderBaseAddress"</span>: <span class="token string">&quot;서울특별시 성동구 왕십리로 58&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"senderDetailAddress"</span>: <span class="token string">&quot;서울숲포휴 503호&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"senderName"</span>: <span class="token string">&quot;홍길동&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"senderPhoneNumber"</span>: <span class="token string">&quot;010-1234-5678&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"viaAddress"</span>: <span class="token string">&quot;서울특별시 강남구 삼성로 212&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"viaDetailAddress"</span>: <span class="token string">&quot;24동 101호&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"viaName"</span>: <span class="token string">&quot;CU은마점&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"viaNote"</span>: <span class="token string">&quot;현관문앞에 놔두세요&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"viaOwner"</span>: <span class="token string">&quot;홍길동&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"viaPhone"</span>: <span class="token string">&quot;010-1234-5678&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"viaType"</span>: <span class="token string">&quot;방문지&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"visitType"</span>: <span class="token string">&quot;배송&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"warehouseCode"</span>: <span class="token string">&quot;강남HUB입고센터&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"zipCode"</span>: <span class="token string">&quot;06284&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"projectName"</span>: <span class="token string">&quot;강남A-1회차배송&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kkmypM jKNCPF"> <!-- -->Response samples<!-- --> </h3><div class="sc-bSlUec jxQggo" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:Rjidq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:Rjidq:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:Rjidq:1" aria-selected="false" aria-disabled="false" aria-controls="panel:Rjidq:1" data-rttab="true">401</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:Rjidq:0" aria-labelledby="tab:Rjidq:0"><div><div class="sc-WChnI iLXIA-d"><span class="sc-foMnoT dmNpjh">Content type</span><div class="sc-ZubPq KmnOC">application/json</div></div><div class="sc-AmOMz gDrQpr"><div class="sc-eTdEpr dgzSkD"><div class="sc-pFPEP bMXXJy"><button><div class="sc-eHujzY ecCAmX">Copy</div></button></div><div class="sc-euGpHm fwfkcU sc-fTgapq bDqsIm"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"projectId"</span>: <span class="token string">&quot;10001&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:Rjidq:1" aria-labelledby="tab:Rjidq:1"></div></div></div></div></div></div><div id="tag/TMS/operation/tms-order-register-with-product" data-section-id="tag/TMS/operation/tms-order-register-with-product" class="sc-dsLQwm kcRA-dj"><div data-section-id="operation/tms-order-register-with-product" id="operation/tms-order-register-with-product" class="sc-la-DxNn dSIRVR"><div class="sc-fQpRED htdgPt"><h2 class="sc-knesRu cbpGTP"><a class="sc-jCbFiK hSvuOo" href="#tag/TMS/operation/tms-order-register-with-product" aria-label="tag/TMS/operation/tms-order-register-with-product"></a>주문 등록 (상품정보 포함)<!-- --> </h2><div class="sc-fpJhiv cQxXyG"><div html="&lt;p&gt;현대백화점의 주문 등록시 사용하는 API입니다.&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;주문정보 등록시 상품정보를 포함하여 등록합니다.&lt;/li&gt;
&lt;/ul&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"><p>현대백화점의 주문 등록시 사용하는 API입니다.</p>
<ul>
<li>주문정보 등록시 상품정보를 포함하여 등록합니다.</li>
</ul>
</div></div><div class="sc-hWiVxH cCfxdS"><div class="sc-jHofgq febKCY"><h5 class="sc-dkjaqt sc-jRHPII gwrByh eGMvsx">Authorizations:</h5><svg class="sc-cBYhjr dJanPw" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-iRFtIJ juVMxG"><span class="sc-iHlJgr lkBZak"><span class="sc-blcnQh fyveIh"><i>BearerAuth</i></span></span></div></div><h5 class="sc-dkjaqt gwrByh">Request Body schema: <span class="sc-dwYcXH fafqZb">application/json</span></h5><div html="" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"></div><table class="sc-dENhDJ ceVHDP"><tbody><tr class=""><td kind="field" title="customerOrderId" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">customerOrderId</span><div class="sc-xuUkR sc-hrDJJk bJcEcT gLEAmN">required</div></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;고객사에서 관리하는 고유 주문 ID (운송장번호, 전표번호 등)&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>고객사에서 관리하는 고유 주문 ID (운송장번호, 전표번호 등)</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="qrBarCode" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">qrBarCode</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;바코드 상하차에 사용되는 코드. 미입력시 customerOrderId와 동일하게 설정함&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>바코드 상하차에 사용되는 코드. 미입력시 customerOrderId와 동일하게 설정함</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="viaName" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">viaName</span><div class="sc-xuUkR sc-hrDJJk bJcEcT gLEAmN">required</div></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;수신인 배송지 상호등 명칭. 상호명이 없을 경우 viaOwner와 동일하게 수신자 이름을 기입&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>수신인 배송지 상호등 명칭. 상호명이 없을 경우 viaOwner와 동일하게 수신자 이름을 기입</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="viaOwner" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">viaOwner</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;수신인 이름&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>수신인 이름</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="viaAddress" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">viaAddress</span><div class="sc-xuUkR sc-hrDJJk bJcEcT gLEAmN">required</div></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;수신인의 기본 주소&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>수신인의 기본 주소</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="viaDetailAddress" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">viaDetailAddress</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;수신인의 상세 주소&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>수신인의 상세 주소</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="viaPhone" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">viaPhone</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;수신인의 가상 전화번호&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>수신인의 가상 전화번호</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="realPhoneNumber" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">realPhoneNumber</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;수신인의 실제 전화번호&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>수신인의 실제 전화번호</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="viaNote" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">viaNote</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;배송시 메모 내용&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>배송시 메모 내용</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="viaType" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">viaType</span><div class="sc-xuUkR sc-hrDJJk bJcEcT gLEAmN">required</div></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div><div><span class="sc-xuUkR bJcEcT"> <!-- -->Enum<!-- -->:</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;방문지&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;물류거점&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;물류센터&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;회차지점&quot;</span> </div> <div><div html="&lt;p&gt;배송의 종류 (&amp;#39;방문지&amp;#39;로 고정합니다.)&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>배송의 종류 (&#39;방문지&#39;로 고정합니다.)</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="productName" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">productName</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;배송 상품 정보&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>배송 상품 정보</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="productQuantity" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">productQuantity</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">integer</span></div> <div><div html="&lt;p&gt;총 상품 수량. 묶음배송일시에 총 상품의 갯수를 입력한다 .&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>총 상품 수량. 묶음배송일시에 총 상품의 갯수를 입력한다 .</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="storageType" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">storageType</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div><div><span class="sc-xuUkR bJcEcT"> <!-- -->Enum<!-- -->:</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;1-1 상온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;1-2 상온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;2-1 상온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;2-2 상온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;3-1 상온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;3-2 상온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;4-1 저온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;4-2 저온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;5-1 저온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;5-2 저온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;6-1 저온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;6-2 저온&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;7-1 VIP&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;7-2 VIP&quot;</span> </div> <div><div html="&lt;p&gt;호차 구분&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>호차 구분</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="deliveryCategory" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">deliveryCategory</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div><div><span class="sc-xuUkR bJcEcT"> <!-- -->Enum<!-- -->:</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;근거리배송&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;원거리배송&quot;</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">&quot;명절배송&quot;</span> </div> <div><div html="&lt;p&gt;근거리/원거리/명절 배송으로 구분된다.&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>근거리/원거리/명절 배송으로 구분된다.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="senderName" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">senderName</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;보내는 사람 이름&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>보내는 사람 이름</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="senderCompanyName" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">senderCompanyName</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;보내는 사람 회사명&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>보내는 사람 회사명</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="senderPhoneNumber" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">senderPhoneNumber</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;보내는 사람 전화번호&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>보내는 사람 전화번호</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="senderBaseAddress" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">senderBaseAddress</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;보내는 사람 주소&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>보내는 사람 주소</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="senderDetailAddress" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">senderDetailAddress</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;보내는 사람 상세주소&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>보내는 사람 상세주소</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="senderImageUrl" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">senderImageUrl</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;보내는 사람 명함 등 이미지 URL&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>보내는 사람 명함 등 이미지 URL</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="deliveryTime" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">deliveryTime</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;배송 일자&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>배송 일자</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="requestDeliveryTime" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">requestDeliveryTime</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;배송 예정 시간. 고객이 배송되기 원하는 시간을 입력함. 없을 경우 비워둠&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>배송 예정 시간. 고객이 배송되기 원하는 시간을 입력함. 없을 경우 비워둠</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="joCd" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">joCd</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;조코드 (001 ~ 009)&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>조코드 (001 ~ 009)</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="whGubn" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">whGubn</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">integer</span></div><div><span class="sc-xuUkR bJcEcT"> <!-- -->Enum<!-- -->:</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">1</span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">2</span> </div> <div><div html="" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"></div></div></div></td></tr><tr class=""><td kind="field" title="warehouseCode" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">warehouseCode</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;물류센터코드&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>물류센터코드</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="storeCd" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">storeCd</span></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><div html="&lt;p&gt;점코드&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>점코드</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="orderItemList" class="sc-tOkKi sc-epPVmt sc-hxyskk gbdrVc bUksBx fWeVrc"><span class="sc-hfvVTD hTjFRU"></span><button aria-label="expand orderItemList"><span class="property-name">orderItemList</span><svg class="sc-cBYhjr MHxpd" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG">Array of </span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">objects</span><span class="sc-xuUkR sc-grBnJl bJcEcT jljzwO"> (<!-- -->OrderItemDTO<!-- -->) </span></div> <div><div html="" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"></div></div></div></td></tr></tbody></table><div><h3 class="sc-eiLgtK dbBFCU">Responses</h3><div><button class="sc-giOWAb gcYfHW"><svg class="sc-cBYhjr fqtTpb" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-catHVh gXPvFO">200<!-- --> </strong><div html="&lt;p&gt;OK (성공)&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>OK (성공)</p>
</div></button></div><div><button class="sc-giOWAb gcYfHW"><svg class="sc-cBYhjr fqtTpb" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-catHVh gXPvFO">206<!-- --> </strong><div html="&lt;p&gt;Partial Content (주소정제 실패)&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>Partial Content (주소정제 실패)</p>
</div></button></div><div><button class="sc-giOWAb bEasFS" disabled=""><strong class="sc-catHVh gXPvFO">400<!-- --> </strong><div html="&lt;p&gt;Bad Request (잘못된 요청)&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>Bad Request (잘못된 요청)</p>
</div></button></div><div><button class="sc-giOWAb bEasFS" disabled=""><strong class="sc-catHVh gXPvFO">404<!-- --> </strong><div html="&lt;p&gt;Not Found (찾을 수 없음)&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>Not Found (찾을 수 없음)</p>
</div></button></div><div><button class="sc-giOWAb bEasFS" disabled=""><strong class="sc-catHVh gXPvFO">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error (서버 오류)&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>Internal Server Error (서버 오류)</p>
</div></button></div></div></div><div class="sc-iKTcqh sc-gnpbhQ fTxZsC drGLlX"><div class="sc-dKsqdn dCbPd"><button class="sc-iAlELC gsBSOU"><span type="post" class="sc-oeqTF kpMtuJ http-verb post">post</span><span class="sc-eowDPD jcAXWA">/openapi/v2/deliveries</span><svg class="sc-cBYhjr iMxoRf" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ezTrPE bFiOkX"><div class="sc-drnuxz hdRKqQ"><div html="&lt;p&gt;Development&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Development</p>
</div><div tabindex="0" role="button"><div class="sc-hDcvty jpmGrk"><span>https://aloa-dev.logisteq.com</span>/openapi/v2/deliveries</div></div></div><div class="sc-drnuxz hdRKqQ"><div html="&lt;p&gt;Stage&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Stage</p>
</div><div tabindex="0" role="button"><div class="sc-hDcvty jpmGrk"><span>https://aloa-qa.logisteq.com</span>/openapi/v2/deliveries</div></div></div></div></div><div><h3 class="sc-kkmypM jKNCPF"> <!-- -->Request samples<!-- --> </h3><div class="sc-bSlUec jxQggo" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:Riilq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:Riilq:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:Riilq:0" aria-labelledby="tab:Riilq:0"><div><div class="sc-WChnI iLXIA-d"><span class="sc-foMnoT dmNpjh">Content type</span><div class="sc-ZubPq KmnOC">application/json</div></div><div class="sc-AmOMz gDrQpr"><div class="sc-eTdEpr dgzSkD"><div class="sc-pFPEP bMXXJy"><button><div class="sc-eHujzY ecCAmX">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-euGpHm fwfkcU sc-fTgapq bDqsIm"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"customerOrderId"</span>: <span class="token string">&quot;1012345678001&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"qrBarCode"</span>: <span class="token string">&quot;1012345678001&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"viaName"</span>: <span class="token string">&quot;CU은마점&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"viaOwner"</span>: <span class="token string">&quot;홍길동&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"viaAddress"</span>: <span class="token string">&quot;서울특별시 강남구 삼성로 212&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"viaDetailAddress"</span>: <span class="token string">&quot;24동 101호&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"viaPhone"</span>: <span class="token string">&quot;5757937442&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"realPhoneNumber"</span>: <span class="token string">&quot;01056784321&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"viaNote"</span>: <span class="token string">&quot;현관문 앞에 놔두세요&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"viaType"</span>: <span class="token string">&quot;방문지&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"productName"</span>: <span class="token string">&quot;영광 특선 참굴비 외 2개&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"productQuantity"</span>: <span class="token number">3</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"storageType"</span>: <span class="token string">&quot;1-1 상온&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"deliveryCategory"</span>: <span class="token string">&quot;근거리배송&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"senderName"</span>: <span class="token string">&quot;김현백&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"senderCompanyName"</span>: <span class="token string">&quot;현대상사&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"senderPhoneNumber"</span>: <span class="token string">&quot;01098765432&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"senderBaseAddress"</span>: <span class="token string">&quot;서울특별시 강남구 삼성로 212&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"senderDetailAddress"</span>: <span class="token string">&quot;502호 현대상사&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"senderImageUrl"</span>: <span class="token string">&quot;</span><a href="https://file-examples.com/wp-content/uploads/2018/03/file_example_TIFF_1MB.tiff">https://file-examples.com/wp-content/uploads/2018/03/file_example_TIFF_1MB.tiff</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"deliveryTime"</span>: <span class="token string">&quot;20240521&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"requestDeliveryTime"</span>: <span class="token string">&quot;20240521 14:00&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"joCd"</span>: <span class="token string">&quot;001&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"whGubn"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"warehouseCode"</span>: <span class="token string">&quot;경인센터&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"storeCd"</span>: <span class="token string">&quot;천호점&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"orderItemList"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"itemCode"</span>: <span class="token string">&quot;7894324&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"productBarcode"</span>: <span class="token string">&quot;10-78&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"itemName"</span>: <span class="token string">&quot;한우 등심 혼합&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"orderQuantity"</span>: <span class="token number">1</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kkmypM jKNCPF"> <!-- -->Response samples<!-- --> </h3><div class="sc-bSlUec jxQggo" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:Rjilq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:Rjilq:0" tabindex="0" data-rttab="true">200</li><li class="tab-success" role="tab" id="tab:Rjilq:1" aria-selected="false" aria-disabled="false" aria-controls="panel:Rjilq:1" data-rttab="true">206</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:Rjilq:0" aria-labelledby="tab:Rjilq:0"><div><div class="sc-WChnI iLXIA-d"><span class="sc-foMnoT dmNpjh">Content type</span><div class="sc-ZubPq KmnOC">application/json</div></div><div class="sc-AmOMz gDrQpr"><div class="sc-eTdEpr dgzSkD"><div class="sc-pFPEP bMXXJy"><button><div class="sc-eHujzY ecCAmX">Copy</div></button></div><div class="sc-euGpHm fwfkcU sc-fTgapq bDqsIm"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"projectId"</span>: <span class="token number">12345</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:Rjilq:1" aria-labelledby="tab:Rjilq:1"></div></div></div></div></div></div><div id="tag/TMS/operation/tms-order-inquiry" data-section-id="tag/TMS/operation/tms-order-inquiry" class="sc-dsLQwm kcRA-dj"><div data-section-id="operation/tms-order-inquiry" id="operation/tms-order-inquiry" class="sc-la-DxNn dSIRVR"><div class="sc-fQpRED htdgPt"><h2 class="sc-knesRu cbpGTP"><a class="sc-jCbFiK hSvuOo" href="#tag/TMS/operation/tms-order-inquiry" aria-label="tag/TMS/operation/tms-order-inquiry"></a>배송지 주문 조회 (단건)<!-- --> </h2><div class="sc-fpJhiv cQxXyG"><div html="&lt;p&gt;배송지의 주문 정보의 상태를 조회합니다&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"><p>배송지의 주문 정보의 상태를 조회합니다</p>
</div></div><div class="sc-hWiVxH cCfxdS"><div class="sc-jHofgq febKCY"><h5 class="sc-dkjaqt sc-jRHPII gwrByh eGMvsx">Authorizations:</h5><svg class="sc-cBYhjr dJanPw" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-iRFtIJ juVMxG"><span class="sc-iHlJgr lkBZak"><span class="sc-blcnQh fyveIh"><i>BearerAuth</i></span></span></div></div><div><h5 class="sc-dkjaqt gwrByh">path<!-- --> Parameters</h5><table class="sc-dENhDJ ceVHDP"><tbody><tr class="last "><td kind="field" title="customerOrderId" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">customerOrderId</span><div class="sc-xuUkR sc-hrDJJk bJcEcT gLEAmN">required</div></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG"></span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">string</span></div> <div><span class="sc-xuUkR bJcEcT"> <!-- -->Example:<!-- --> </span> <span class="sc-xuUkR sc-cDelgQ bJcEcT iWjUHw">A1234567890</span></div><div><div html="&lt;p&gt;주문 조회하고자 하는 customerOrderId&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>주문 조회하고자 하는 customerOrderId</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-eiLgtK dbBFCU">Responses</h3><div><button class="sc-giOWAb gcYfHW"><svg class="sc-cBYhjr fqtTpb" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-catHVh gXPvFO">200<!-- --> </strong><div html="&lt;p&gt;주문정보 조회 성공&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>주문정보 조회 성공</p>
</div></button></div><div><button class="sc-giOWAb dSgpsc" disabled=""><strong class="sc-catHVh gXPvFO">204<!-- --> </strong><div html="&lt;p&gt;조회된 주문정보가 존재하지 않음&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>조회된 주문정보가 존재하지 않음</p>
</div></button></div><div><button class="sc-giOWAb gbPhWg"><svg class="sc-cBYhjr ezFOZv" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-catHVh gXPvFO">401<!-- --> </strong><div html="&lt;p&gt;Unauthorized&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>Unauthorized</p>
</div></button></div></div></div><div class="sc-iKTcqh sc-gnpbhQ fTxZsC drGLlX"><div class="sc-dKsqdn dCbPd"><button class="sc-iAlELC gsBSOU"><span type="get" class="sc-oeqTF ffmPnn http-verb get">get</span><span class="sc-eowDPD jcAXWA">/openapi/customers/deliveries/{customerOrderId}</span><svg class="sc-cBYhjr iMxoRf" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ezTrPE bFiOkX"><div class="sc-drnuxz hdRKqQ"><div html="&lt;p&gt;Development&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Development</p>
</div><div tabindex="0" role="button"><div class="sc-hDcvty jpmGrk"><span>https://aloa-dev.logisteq.com</span>/openapi/customers/deliveries/{customerOrderId}</div></div></div><div class="sc-drnuxz hdRKqQ"><div html="&lt;p&gt;Stage&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Stage</p>
</div><div tabindex="0" role="button"><div class="sc-hDcvty jpmGrk"><span>https://aloa-qa.logisteq.com</span>/openapi/customers/deliveries/{customerOrderId}</div></div></div></div></div><div><h3 class="sc-kkmypM jKNCPF"> <!-- -->Response samples<!-- --> </h3><div class="sc-bSlUec jxQggo" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:Rjitq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:Rjitq:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:Rjitq:1" aria-selected="false" aria-disabled="false" aria-controls="panel:Rjitq:1" data-rttab="true">401</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:Rjitq:0" aria-labelledby="tab:Rjitq:0"><div><div class="sc-WChnI iLXIA-d"><span class="sc-foMnoT dmNpjh">Content type</span><div class="sc-ZubPq KmnOC">application/json</div></div><div class="sc-AmOMz gDrQpr"><div class="sc-eTdEpr dgzSkD"><div class="sc-pFPEP bMXXJy"><button><div class="sc-eHujzY ecCAmX">Copy</div></button></div><div class="sc-euGpHm fwfkcU sc-fTgapq bDqsIm"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"customerOrderId"</span>: <span class="token string">&quot;A1234567890&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"receiverName"</span>: <span class="token string">&quot;CU은마점&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"productName"</span>: <span class="token string">&quot;신선사과&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"deliveryRequestAt"</span>: <span class="token string">&quot;2024-03-21T12:32:56&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"visitType"</span>: <span class="token string">&quot;배송&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"qrBarCode"</span>: <span class="token string">&quot;A1234567890&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"projectName"</span>: <span class="token string">&quot;강남A-1회차배송&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"address"</span>: <span class="token string">&quot;서울특별시 강남구 삼성로 212&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:Rjitq:1" aria-labelledby="tab:Rjitq:1"></div></div></div></div></div></div><div id="tag/TMS/operation/tms-order-inquiry-multiple" data-section-id="tag/TMS/operation/tms-order-inquiry-multiple" class="sc-dsLQwm kcRA-dj"><div data-section-id="operation/tms-order-inquiry-multiple" id="operation/tms-order-inquiry-multiple" class="sc-la-DxNn dSIRVR"><div class="sc-fQpRED htdgPt"><h2 class="sc-knesRu cbpGTP"><a class="sc-jCbFiK hSvuOo" href="#tag/TMS/operation/tms-order-inquiry-multiple" aria-label="tag/TMS/operation/tms-order-inquiry-multiple"></a>배송지 주문 조회 (다건)<!-- --> </h2><div class="sc-fpJhiv cQxXyG"><div html="&lt;p&gt;배송지의 주문 정보의 상태를 조회합니다&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"><p>배송지의 주문 정보의 상태를 조회합니다</p>
</div></div><div class="sc-hWiVxH cCfxdS"><div class="sc-jHofgq febKCY"><h5 class="sc-dkjaqt sc-jRHPII gwrByh eGMvsx">Authorizations:</h5><svg class="sc-cBYhjr dJanPw" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-iRFtIJ juVMxG"><span class="sc-iHlJgr lkBZak"><span class="sc-blcnQh fyveIh"><i>BearerAuth</i></span></span></div></div><h5 class="sc-dkjaqt gwrByh">Request Body schema: <span class="sc-dwYcXH fafqZb">application/json</span></h5><div html="" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD"></div><table class="sc-dENhDJ ceVHDP"><tbody><tr class="last "><td kind="field" title="orderIdList" class="sc-tOkKi sc-epPVmt gbdrVc bUksBx"><span class="sc-hfvVTD hTjFRU"></span><span class="property-name">orderIdList</span><div class="sc-xuUkR sc-hrDJJk bJcEcT gLEAmN">required</div></td><td class="sc-fpSrms exGrJC"><div><div><span class="sc-xuUkR sc-cvzDha bJcEcT kDPMlG">Array of </span><span class="sc-xuUkR sc-gKROGD bJcEcT etUsjc">strings</span></div> <div><div html="&lt;p&gt;조회하고자 하는 주문번호의 리스트를 입력&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>조회하고자 하는 주문번호의 리스트를 입력</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-eiLgtK dbBFCU">Responses</h3><div><button class="sc-giOWAb gcYfHW"><svg class="sc-cBYhjr fqtTpb" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-catHVh gXPvFO">200<!-- --> </strong><div html="&lt;p&gt;주문정보 조회 성공. 조회된 배송지가 없을시 빈배열을 리턴&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>주문정보 조회 성공. 조회된 배송지가 없을시 빈배열을 리턴</p>
</div></button></div><div><button class="sc-giOWAb gbPhWg"><svg class="sc-cBYhjr ezFOZv" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-catHVh gXPvFO">401<!-- --> </strong><div html="&lt;p&gt;Unauthorized&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU kqJXdD sc-dHrNzZ dRdjww"><p>Unauthorized</p>
</div></button></div></div></div><div class="sc-iKTcqh sc-gnpbhQ fTxZsC drGLlX"><div class="sc-dKsqdn dCbPd"><button class="sc-iAlELC gsBSOU"><span type="post" class="sc-oeqTF kpMtuJ http-verb post">post</span><span class="sc-eowDPD jcAXWA">/openapi/customers/deliveries/customerOrderIds</span><svg class="sc-cBYhjr iMxoRf" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ezTrPE bFiOkX"><div class="sc-drnuxz hdRKqQ"><div html="&lt;p&gt;Development&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Development</p>
</div><div tabindex="0" role="button"><div class="sc-hDcvty jpmGrk"><span>https://aloa-dev.logisteq.com</span>/openapi/customers/deliveries/customerOrderIds</div></div></div><div class="sc-drnuxz hdRKqQ"><div html="&lt;p&gt;Stage&lt;/p&gt;
" class="sc-euGpHm sc-exayXG fwfkcU jYGAQp"><p>Stage</p>
</div><div tabindex="0" role="button"><div class="sc-hDcvty jpmGrk"><span>https://aloa-qa.logisteq.com</span>/openapi/customers/deliveries/customerOrderIds</div></div></div></div></div><div><h3 class="sc-kkmypM jKNCPF"> <!-- -->Request samples<!-- --> </h3><div class="sc-bSlUec jxQggo" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:Rij5q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:Rij5q:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:Rij5q:0" aria-labelledby="tab:Rij5q:0"><div><div class="sc-WChnI iLXIA-d"><span class="sc-foMnoT dmNpjh">Content type</span><div class="sc-ZubPq KmnOC">application/json</div></div><div class="sc-AmOMz gDrQpr"><div class="sc-eTdEpr dgzSkD"><div class="sc-pFPEP bMXXJy"><button><div class="sc-eHujzY ecCAmX">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-euGpHm fwfkcU sc-fTgapq bDqsIm"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"orderIdList"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;A1234567890&quot;</span>,</div></li><li><div class="hoverable collapsed"><span class="token string">&quot;A000000002&quot;</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kkmypM jKNCPF"> <!-- -->Response samples<!-- --> </h3><div class="sc-bSlUec jxQggo" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:Rjj5q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:Rjj5q:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:Rjj5q:1" aria-selected="false" aria-disabled="false" aria-controls="panel:Rjj5q:1" data-rttab="true">401</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:Rjj5q:0" aria-labelledby="tab:Rjj5q:0"><div><div class="sc-WChnI iLXIA-d"><span class="sc-foMnoT dmNpjh">Content type</span><div class="sc-ZubPq KmnOC">application/json</div></div><div class="sc-AmOMz gDrQpr"><div class="sc-eTdEpr dgzSkD"><div class="sc-pFPEP bMXXJy"><button><div class="sc-eHujzY ecCAmX">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-euGpHm fwfkcU sc-fTgapq bDqsIm"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"customerOrderId"</span>: <span class="token string">&quot;A1234567890&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"receiverName"</span>: <span class="token string">&quot;CU은마점&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"productName"</span>: <span class="token string">&quot;신선사과&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"deliveryRequestAt"</span>: <span class="token string">&quot;2024-03-21T12:32:56&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"visitType"</span>: <span class="token string">&quot;배송&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"qrBarCode"</span>: <span class="token string">&quot;A1234567890&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"projectName"</span>: <span class="token string">&quot;강남A-1회차배송&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"address"</span>: <span class="token string">&quot;서울특별시 강남구 삼성로 212&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:Rjj5q:1" aria-labelledby="tab:Rjj5q:1"></div></div></div></div></div></div></div><div class="sc-epzHnm bWkBKa"></div></div></div>
      <script>
      const __redoc_state = {"menu":{"activeItemIdx":-1},"spec":{"data":{"openapi":"3.0.1","info":{"title":"ALOA Open API","description":"## 알로아 고객용 OpenApi","license":{"name":"License Logisteq"},"version":"1.0.1"},"servers":[{"url":"https://aloa-dev.logisteq.com","description":"Development"},{"url":"https://aloa-qa.logisteq.com","description":"Stage"}],"tags":[{"name":"Auth","description":"ALOA 접근키 발급"},{"name":"TMS","description":"ALOA 주문등록"}],"paths":{"/oauth/token":{"post":{"tags":["Auth"],"summary":"억세스 토큰 발급","description":"고객사의 ID/Password 정보로 Auth token을 발급합니다. 기본적인 유효 기간은 3개월입니다","operationId":"auth-token-request","parameters":[{"name":"grant_type","description":"Type of grant requested (client_credentials 로 요청 )","required":true,"in":"query","schema":{"type":"string","example":"client_credentials","default":"client_credentials"}},{"name":"scope","description":"Scope of access requested (read write 로 요청)","required":true,"in":"query","schema":{"type":"string","example":"read write","default":"read write"}}],"security":[{"BasicAuth":[]}],"responses":{"200":{"description":"Auth Token 수신 발급 완료","content":{"application/json":{"schema":{"type":"object","properties":{"access_token":{"type":"string","example":"eyJhbGciOiJ...SUzI1NiI","description":"발급된 인증 억세스 토큰. API 호출시 header에 Authorization 의 값으로 추가 하여 요청해야 함"},"token_type":{"type":"string","example":"bearer","default":"bearer"},"expires_in":{"type":"integer","example":15551999,"description":"토큰 남은 유효시간 (seconds) 1551999초 = 약 180일 ( 6개월 )"},"scope":{"type":"string","example":"read write"},"jti":{"type":"string","example":"cc294c6c-ff24-4aa8-8db5-aa1019d2e706"}}}}}},"400":{"description":"누락된 파라미터 또는 잘못된 파라미터 오류"},"401":{"description":"인증 정보 오류","content":{"application/json":{"schema":{"type":"object","properties":{"timestamp":{"type":"string","format":"date-time","example":"2024-03-20T01:34:17.593+0000"},"status":{"type":"integer","example":401},"error":{"type":"string","example":"Unauthorized"},"message":{"type":"string","example":"Unauthorized"},"path":{"type":"string","example":"/oauth/token"}}}}}}}}},"/openapi/deliveries/project":{"post":{"tags":["TMS"],"summary":"배송지 주문 등록","description":"프로젝트 정보를 포함한 배송지 주문 등록","operationId":"tms-order-register","security":[{"BearerAuth":[]}],"requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/RequestOrderWithProjectDTO"}}}},"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"object","properties":{"projectId":{"type":"integer","example":"10001"}}}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResponseUnauthorized"}}}}}}},"/openapi/v2/deliveries":{"post":{"tags":["TMS"],"summary":"주문 등록 (상품정보 포함)","description":"현대백화점의 주문 등록시 사용하는 API입니다.\n\n- 주문정보 등록시 상품정보를 포함하여 등록합니다.","operationId":"tms-order-register-with-product","security":[{"BearerAuth":[]}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"customerOrderId":{"type":"string","description":"고객사에서 관리하는 고유 주문 ID (운송장번호, 전표번호 등)","example":"1012345678001"},"qrBarCode":{"type":"string","description":"바코드 상하차에 사용되는 코드. 미입력시 customerOrderId와 동일하게 설정함","example":"1012345678001"},"viaName":{"type":"string","description":"수신인 배송지 상호등 명칭. 상호명이 없을 경우 viaOwner와 동일하게 수신자 이름을 기입","example":"CU은마점"},"viaOwner":{"type":"string","description":"수신인 이름","example":"홍길동"},"viaAddress":{"type":"string","description":"수신인의 기본 주소","example":"서울특별시 강남구 삼성로 212"},"viaDetailAddress":{"type":"string","description":"수신인의 상세 주소","example":"24동 101호"},"viaPhone":{"type":"string","description":"수신인의 가상 전화번호","example":"5757937442"},"realPhoneNumber":{"type":"string","description":"수신인의 실제 전화번호","example":"01056784321"},"viaNote":{"type":"string","description":"배송시 메모 내용","example":"현관문 앞에 놔두세요"},"viaType":{"type":"string","enum":["방문지","물류거점","물류센터","회차지점"],"description":"배송의 종류 ('방문지'로 고정합니다.)","example":"방문지"},"productName":{"type":"string","description":"배송 상품 정보","example":"영광 특선 참굴비 외 2개"},"productQuantity":{"type":"integer","description":"총 상품 수량. 묶음배송일시에 총 상품의 갯수를 입력한다 .","example":3},"storageType":{"type":"string","description":"호차 구분","example":"1-1 상온","enum":["1-1 상온","1-2 상온","2-1 상온","2-2 상온","3-1 상온","3-2 상온","4-1 저온","4-2 저온","5-1 저온","5-2 저온","6-1 저온","6-2 저온","7-1 VIP","7-2 VIP"]},"deliveryCategory":{"type":"string","description":"근거리/원거리/명절 배송으로 구분된다.","example":"근거리배송","enum":["근거리배송","원거리배송","명절배송"]},"senderName":{"type":"string","description":"보내는 사람 이름","example":"김현백"},"senderCompanyName":{"type":"string","description":"보내는 사람 회사명","example":"현대상사"},"senderPhoneNumber":{"type":"string","description":"보내는 사람 전화번호","example":"01098765432"},"senderBaseAddress":{"type":"string","description":"보내는 사람 주소","example":"서울특별시 강남구 삼성로 212"},"senderDetailAddress":{"type":"string","description":"보내는 사람 상세주소","example":"502호 현대상사"},"senderImageUrl":{"type":"string","description":"보내는 사람 명함 등 이미지 URL","example":"https://file-examples.com/wp-content/uploads/2018/03/file_example_TIFF_1MB.tiff"},"deliveryTime":{"type":"string","description":"배송 일자","example":"20240521"},"requestDeliveryTime":{"type":"string","example":"20240521 14:00","description":"배송 예정 시간. 고객이 배송되기 원하는 시간을 입력함. 없을 경우 비워둠"},"joCd":{"type":"string","description":"조코드 (001 ~ 009)","example":"001"},"whGubn":{"type":"integer","example":1,"enum":[1,2]},"warehouseCode":{"type":"string","example":"경인센터","description":"물류센터코드"},"storeCd":{"type":"string","example":"천호점","description":"점코드"},"orderItemList":{"type":"array","items":{"$ref":"#/components/schemas/OrderItemDTO"}}},"required":["customerOrderId","viaName","viaAddress","viaType"]}}}},"responses":{"200":{"description":"OK (성공)","content":{"application/json":{"schema":{"type":"object","properties":{"projectId":{"type":"integer","example":12345}}}}}},"206":{"description":"Partial Content (주소정제 실패)","content":{"application/json":{"schema":{"type":"object","properties":{"projectId":{"type":"integer","example":12345},"reason":{"type":"string","example":"잘못된 주소지 정보입니다."},"geoCodingFails":{"type":"array","items":{"type":"object","example":{"customerOrderId":"1010036875018","whGubn":1,"viaAddress":"..."}}}}}}}},"400":{"description":"Bad Request (잘못된 요청)"},"404":{"description":"Not Found (찾을 수 없음)"},"500":{"description":"Internal Server Error (서버 오류)"}}}},"/openapi/customers/deliveries/{customerOrderId}":{"get":{"tags":["TMS"],"summary":"배송지 주문 조회 (단건)","description":"배송지의 주문 정보의 상태를 조회합니다","operationId":"tms-order-inquiry","security":[{"BearerAuth":[]}],"parameters":[{"name":"customerOrderId","description":"주문 조회하고자 하는 customerOrderId","required":true,"in":"path","schema":{"type":"string","example":"A1234567890"}}],"responses":{"200":{"description":"주문정보 조회 성공","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResponseDeliveryOrder"}}}},"204":{"description":"조회된 주문정보가 존재하지 않음"},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResponseUnauthorized"}}}}}}},"/openapi/customers/deliveries/customerOrderIds":{"post":{"tags":["TMS"],"summary":"배송지 주문 조회 (다건)","description":"배송지의 주문 정보의 상태를 조회합니다","operationId":"tms-order-inquiry-multiple","security":[{"BearerAuth":[]}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"orderIdList":{"description":"조회하고자 하는 주문번호의 리스트를 입력","type":"array","items":{"type":"string"},"example":["A1234567890","A000000002"]}},"required":["orderIdList"]}}}},"responses":{"200":{"description":"주문정보 조회 성공. 조회된 배송지가 없을시 빈배열을 리턴","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/ResponseDeliveryOrder"}}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResponseUnauthorized"}}}}}}}},"components":{"securitySchemes":{"BearerAuth":{"type":"http","scheme":"bearer","bearerFormat":"JWT"},"BasicAuth":{"type":"http","scheme":"basic"}},"schemas":{"ResponseUnauthorized":{"type":"object","properties":{"error":{"type":"string","example":"unauthorized"},"error_description":{"type":"string","example":"Full authentication is required to access this resource"}}},"RequestOrderWithProjectDTO":{"title":"RequestOrderWithProjectDTO","type":"object","properties":{"destinations":{"type":"array","items":{"$ref":"#/components/schemas/RequestOrderDTO"}},"email":{"type":"string","description":"프로젝트의 소유자의 계정 이메일 주소.","example":"<EMAIL>"},"projectName":{"type":"string","description":"생성될 프로젝트의 이름","example":"강남A-1회차배송"}}},"RequestOrderDTO":{"title":"RequestOrderDTO","required":["viaAddress","viaName","viaType","visitType"],"type":"object","properties":{"customerOrderId":{"type":"string","description":"고유한 고객에서 관리하는 주문 정보 ( 운송장 번호 등 )","example":"A1234567890"},"groupName":{"type":"string","description":"기사의 배차에 사용되는 배송의 권역명","example":"강남A"},"orderAmount":{"type":"integer","format":"int32","description":"주문금액","example":32000},"productName":{"type":"string","description":"배송 물품 정보","example":"신선사과"},"productQuantity":{"type":"integer","format":"int32","description":"배송 물품 갯수","example":1},"productSize":{"type":"string","enum":["소형","극소","대형","이형물","중형","특대"],"description":"배송 물품의 사이즈","example":"대형"},"qrBarCode":{"type":"string","description":"바코드에 사용되는 코드. 보통 customerOrderId 값과 동일하게 입력하여도 무방함","example":"A1234567890"},"senderBaseAddress":{"type":"string","description":"보내는 사람 주소","example":"서울특별시 성동구 왕십리로 58"},"senderDetailAddress":{"type":"string","description":"보내는 사람 상세주소","example":"서울숲포휴 503호"},"senderName":{"type":"string","description":"보내는 사람 이름","example":"홍길동"},"senderPhoneNumber":{"type":"string","description":"보내는 사람 연락처","example":"010-1234-5678"},"viaAddress":{"type":"string","description":"수신자의 기본 주소","example":"서울특별시 강남구 삼성로 212"},"viaDetailAddress":{"type":"string","description":"수신인의 상세 정보","example":"24동 101호"},"viaName":{"type":"string","description":"수신인 배송지 상호등 명칭. 상호명이 없을 경우 viaOwner와 동일하게 수신자 이름을 기입","example":"CU은마점"},"viaNote":{"type":"string","description":"배송시의 메모 사항","example":"현관문앞에 놔두세요"},"viaOwner":{"type":"string","description":"수신인 이름","example":"홍길동"},"viaPhone":{"type":"string","description":"수신의 전화 번호","example":"010-1234-5678"},"viaType":{"type":"string","enum":["방문지","물류거점","물류센터","회차지점"],"description":"배송의 종류 ( '방문지' 로 고정 합니다 )","example":"방문지"},"visitType":{"type":"string","enum":["배송","교환","상차","수거","프리미엄","하차","회수"],"description":"방문지 종류","example":"배송"},"warehouseCode":{"type":"string","description":"중간 입고지 명칭","example":"강남HUB입고센터"},"zipCode":{"type":"string","description":"담당 구역의 우편번호 (이미 우편번호가 존재시에 입력함)","example":"06284"}}},"ResponseDeliveryOrder":{"title":"ResponseDeliveryOrder","type":"object","properties":{"customerOrderId":{"type":"string","description":"주문 번호","example":"A1234567890"},"receiverName":{"type":"string","description":"배송지 명칭","example":"CU은마점"},"productName":{"type":"string","description":"배송 물품","example":"신선사과"},"deliveryRequestAt":{"type":"string","format":"date-time","description":"배송 요청 일시","example":"2024-03-21T12:32:56"},"visitType":{"enum":["배송","교환","상차","수거","프리미엄","하차","회수"],"description":"배송 타입","example":"배송"},"qrBarCode":{"type":"string","description":"배송 물품","example":"A1234567890"},"projectName":{"type":"string","description":"프로젝트 이름","example":"강남A-1회차배송"},"address":{"type":"string","description":"수신자 주소","example":"서울특별시 강남구 삼성로 212"}},"required":["customerOrderId","receiverName","productName","deliveryRequestAt","visitType","qrBarCode","projectName","address"]},"OrderItemDTO":{"type":"object","properties":{"itemCode":{"type":"string","description":"상품코드","example":"7894324"},"productBarcode":{"type":"string","description":"물류코드","example":"10-78"},"itemName":{"type":"string","description":"상품명","example":"한우 등심 혼합"},"orderQuantity":{"type":"integer","description":"상품수량","example":1}},"required":["itemCode","productBarcode","itemName"]}}}}},"searchIndex":{"store":["section/OpenApi","tag/Auth","tag/Auth/operation/auth-token-request","tag/TMS","tag/TMS/operation/tms-order-register","tag/TMS/operation/tms-order-register-with-product","tag/TMS/operation/tms-order-inquiry","tag/TMS/operation/tms-order-inquiry-multiple"],"index":{"version":"2.3.9","fields":["title","description"],"fieldVectors":[["title/0",[0,0.391,1,1.76]],["description/0",[]],["title/1",[2,1.747]],["description/1",[0,0.459,3,1.602]],["title/2",[0,0.448]],["description/2",[0,0.476,2,0.944,4,1.321,5,1.321,6,1.321,7,1.321]],["title/3",[8,2.444]],["description/3",[0,0.394,3,1.754]],["title/4",[0,0.448]],["description/4",[0,0.515,9,1.662]],["title/5",[0,0.456]],["description/5",[0,0.521,10,1.256,11,1.256]],["title/6",[0,0.456]],["description/6",[0,0.509,12,1.776]],["title/7",[0,0.456]],["description/7",[0,0.509,13,1.776]]],"invertedIndex":[["",{"_index":0,"title":{"0":{},"2":{},"4":{},"5":{},"6":{},"7":{}},"description":{"1":{},"2":{},"3":{},"4":{},"5":{},"6":{},"7":{}}}],["3",{"_index":6,"title":{},"description":{"2":{}}}],["aloa",{"_index":3,"title":{},"description":{"1":{},"3":{}}}],["api",{"_index":10,"title":{},"description":{"5":{}}}],["auth",{"_index":2,"title":{"1":{}},"description":{"2":{}}}],["id/password",{"_index":4,"title":{},"description":{"2":{}}}],["oauth/token",{"_index":7,"title":{},"description":{"2":{}}}],["openapi",{"_index":1,"title":{"0":{}},"description":{}}],["openapi/customers/deliveries/customerorderid",{"_index":13,"title":{},"description":{"7":{}}}],["openapi/customers/deliveries/{customerorderid",{"_index":12,"title":{},"description":{"6":{}}}],["openapi/deliveries/project",{"_index":9,"title":{},"description":{"4":{}}}],["openapi/v2/deliveri",{"_index":11,"title":{},"description":{"5":{}}}],["tm",{"_index":8,"title":{"3":{}},"description":{}}],["token",{"_index":5,"title":{},"description":{"2":{}}}]],"pipeline":[]}},"options":{}};

      var container = document.getElementById('redoc');
      Redoc.hydrate(__redoc_state, container);

      </script>
</body>

</html>
