openapi: 3.0.1

info:
  title: ALOA Open API
  description: "## 알로아 고객용 OpenApi"
  license:
    name: License Logisteq
  version: 1.0.1

servers:
- url: https://aloa-dev.logisteq.com
  description: Development
- url: https://aloa-qa.logisteq.com
  description: Stage

tags:
- name: Auth
  description: ALOA 접근키 발급

- name: TMS
  description: ALOA 주문등록



paths:
  "/oauth/token":
    post:
      tags:
      - Auth
      summary: "억세스 토큰 발급"
      description: "고객사의 ID/Password 정보로 Auth token을 발급합니다. 기본적인 유효 기간은 3개월입니다"
      operationId: 'auth-token-request'
      parameters:
      - name: grant_type
        description: "Type of grant requested (client_credentials 로 요청 )"
        required: true
        in: query
        schema:
          type: string
          example: client_credentials
          default: client_credentials
      - name: scope
        description: "Scope of access requested (read write 로 요청)"
        required: true
        in: query
        schema:
          type: string
          example: "read write"
          default: 'read write'
      security:
      - BasicAuth: []
      responses:
        200:
          description: "Auth Token 수신 발급 완료"
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                    example: "eyJhbGciOiJ...SUzI1NiI"
                    description: 발급된 인증 억세스 토큰. API 호출시 header에 Authorization 의 값으로 추가 하여 요청해야 함
                  token_type:
                    type: string
                    example: "bearer"
                    default: "bearer"
                  expires_in:
                    type: integer
                    example: 15551999
                    description: 토큰 남은 유효시간 (seconds) 1551999초 = 약 180일 ( 6개월 ) 
                  scope:
                    type: string
                    example: "read write"
                  jti:
                    type: string
                    example: "cc294c6c-ff24-4aa8-8db5-aa1019d2e706"
        400:
          description: 누락된 파라미터 또는 잘못된 파라미터 오류
        401:
          description: 인증 정보 오류
          content:
            application/json:
              schema:
                type: object
                properties:
                  timestamp:
                    type: string
                    format: date-time
                    example: "2024-03-20T01:34:17.593+0000"
                  status:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: "Unauthorized"
                  message:
                    type: string
                    example: "Unauthorized"
                  path:
                    type: string
                    example: "/oauth/token"



  /openapi/deliveries/project:
    post:
      tags:
      - TMS
      summary: 배송지 주문 등록
      description: 프로젝트 정보를 포함한 배송지 주문 등록
      operationId: "tms-order-register"
      security:
      - BearerAuth: []

      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/RequestOrderWithProjectDTO"

      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  projectId:
                    type: integer
                    example: "10001"

        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ResponseUnauthorized"


  /openapi/v2/deliveries:
    post:
      tags:
      - TMS
      summary: 주문 등록 (상품정보 포함)
      description: |-
        현대백화점의 주문 등록시 사용하는 API입니다.

        - 주문정보 등록시 상품정보를 포함하여 등록합니다.
        
      operationId: "tms-order-register-with-product"
      security:
      - BearerAuth: []

      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                customerOrderId:
                  type: string
                  description: 고객사에서 관리하는 고유 주문 ID (운송장번호, 전표번호 등)
                  example: "1012345678001"

                qrBarCode:
                  type: string
                  description: 바코드 상하차에 사용되는 코드. 미입력시 customerOrderId와 동일하게 설정함
                  example: "1012345678001"

                viaName:
                  type: string
                  description: 수신인 배송지 상호등 명칭. 상호명이 없을 경우 viaOwner와 동일하게 수신자 이름을 기입
                  example: "CU은마점"

                viaOwner:
                  type: string
                  description: 수신인 이름
                  example: "홍길동"

                viaAddress:
                  type: string
                  description: 수신인의 기본 주소
                  example: "서울특별시 강남구 삼성로 212"

                viaDetailAddress:
                  type: string
                  description: 수신인의 상세 주소
                  example: "24동 101호"

                viaPhone:
                  type: string
                  description: 수신인의 가상 전화번호
                  example: "5757937442"

                realPhoneNumber:
                  type: string
                  description: 수신인의 실제 전화번호
                  example: "01056784321"

                viaNote:
                  type: string
                  description: 배송시 메모 내용
                  example: "현관문 앞에 놔두세요"

                viaType:
                  type: string
                  enum:
                  - 방문지
                  - 물류거점
                  - 물류센터
                  - 회차지점
                  description: 배송의 종류 ('방문지'로 고정합니다.)
                  example: "방문지"

                productName:
                  type: string
                  description: 배송 상품 정보
                  example: "영광 특선 참굴비 외 2개"

                productQuantity:
                  type: integer
                  description: 총 상품 수량. 묶음배송일시에 총 상품의 갯수를 입력한다 .
                  example: 3
                
                storageType:
                  type: string
                  description: 호차 구분
                  example: "1-1 상온"
                  enum:
                  - 1-1 상온
                  - 1-2 상온
                  - 2-1 상온
                  - 2-2 상온
                  - 3-1 상온
                  - 3-2 상온
                  - 4-1 저온
                  - 4-2 저온
                  - 5-1 저온
                  - 5-2 저온
                  - 6-1 저온
                  - 6-2 저온
                  - 7-1 VIP
                  - 7-2 VIP


                deliveryCategory:
                  type: string
                  description:  근거리/원거리/명절 배송으로 구분된다.
                  example: "근거리배송"
                  enum:
                  - 근거리배송
                  - 원거리배송
                  - 명절배송

                senderName:
                  type: string
                  description: 보내는 사람 이름
                  example: "김현백"
                
                senderCompanyName:
                  type: string
                  description: 보내는 사람 회사명
                  example: "현대상사"

                senderPhoneNumber:
                  type: string
                  description: 보내는 사람 전화번호
                  example: "01098765432"

                senderBaseAddress:
                  type: string
                  description: 보내는 사람 주소
                  example: "서울특별시 강남구 삼성로 212"

                senderDetailAddress:
                  type: string
                  description: 보내는 사람 상세주소
                  example: "502호 현대상사"

                senderImageUrl:
                  type: string
                  description: 보내는 사람 명함 등 이미지 URL
                  example: "https://file-examples.com/wp-content/uploads/2018/03/file_example_TIFF_1MB.tiff"

                deliveryTime:
                  type: string
                  description: 배송 일자
                  example: "20240521"

                requestDeliveryTime:
                  type: string
                  example: "20240521 14:00"
                  description: 배송 예정 시간. 고객이 배송되기 원하는 시간을 입력함. 없을 경우 비워둠

                joCd:
                  type: string
                  description: 조코드 (001 ~ 009)
                  example: "001"

                whGubn:
                  type: integer
                  example: 1
                  enum:
                  - 1
                  - 2
                
                warehouseCode:
                  type: string
                  example: "경인센터"
                  description: 물류센터코드

                storeCd:
                  type: string
                  example: "천호점"
                  description: 점코드

                orderItemList:
                  type: array
                  items:
                    $ref: '#/components/schemas/OrderItemDTO'

              required:
                - customerOrderId
                - viaName
                - viaAddress
                - viaType
                
      responses:
        '200':
          description: OK (성공)
          content:
            application/json:
              schema:
                type: object
                properties:
                  projectId:
                    type: integer
                    example: 12345

        '206':
          description: Partial Content (주소정제 실패)
          content:
            application/json:
              schema:
                type: object
                properties:
                  projectId:
                    type: integer
                    example: 12345
                  reason:
                    type: string
                    example: "잘못된 주소지 정보입니다."
                  geoCodingFails:
                    type: array
                    items:
                      type: object
                      example: {"customerOrderId":"1010036875018","whGubn":1,"viaAddress":...}

        '400':
          description: Bad Request (잘못된 요청)

        '404':
          description: Not Found (찾을 수 없음)

        '500':
          description: Internal Server Error (서버 오류)




  "/openapi/customers/deliveries/{customerOrderId}":
    get:
      tags:
      - TMS
      summary: 배송지 주문 조회 (단건)
      description: 배송지의 주문 정보의 상태를 조회합니다
      operationId: 'tms-order-inquiry'
      security:
      - BearerAuth: []

      parameters:
      - name: customerOrderId
        description: "주문 조회하고자 하는 customerOrderId"
        required: true
        in: path
        schema:
          type: string
          example: A1234567890

      responses:
        '200':
          description: 주문정보 조회 성공
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ResponseDeliveryOrder"
        '204':
          description: 조회된 주문정보가 존재하지 않음

        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ResponseUnauthorized"




  "/openapi/customers/deliveries/customerOrderIds":
    post:
      tags:
      - TMS
      summary: 배송지 주문 조회 (다건)
      description: 배송지의 주문 정보의 상태를 조회합니다
      operationId: "tms-order-inquiry-multiple"
      security:
      - BearerAuth: []

      requestBody:
        content:
          application/json:
            
            schema:
              type: object
              properties:
                orderIdList:
                  description: 조회하고자 하는 주문번호의 리스트를 입력
                  type: array
                  items:
                    type: string
                  example:
                  - "A1234567890"
                  - "A000000002"
              required:
              - orderIdList

      responses:
        '200':
          description: 주문정보 조회 성공. 조회된 배송지가 없을시 빈배열을 리턴
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/ResponseDeliveryOrder"

        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ResponseUnauthorized"


components:

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    BasicAuth:
      type: http
      scheme: basic

  schemas:
    ResponseUnauthorized:
      type: object
      properties:
        error:
          type: string
          example: "unauthorized"
        error_description:
          type: string
          example: "Full authentication is required to access this resource"

    RequestOrderWithProjectDTO:
      title: RequestOrderWithProjectDTO
      type: object
      properties:
        destinations:
          type: array
          items:
            "$ref": "#/components/schemas/RequestOrderDTO"
        email:
          type: string
          description: 프로젝트의 소유자의 계정 이메일 주소.
          example: <EMAIL>

        projectName:
          type: string
          description: 생성될 프로젝트의 이름
          example: 강남A-1회차배송

    RequestOrderDTO:
      title: RequestOrderDTO
      required:
      - viaAddress
      - viaName
      - viaType
      - visitType
      type: object
      properties:
        customerOrderId:
          type: string
          description: 고유한 고객에서 관리하는 주문 정보 ( 운송장 번호 등 )
          example: A1234567890

        groupName:
          type: string
          description: 기사의 배차에 사용되는 배송의 권역명
          example: 강남A

        orderAmount:
          type: integer
          format: int32
          description: 주문금액
          example: 32000

        productName:
          type: string
          description: 배송 물품 정보
          example: 신선사과

        productQuantity:
          type: integer
          format: int32
          description: 배송 물품 갯수
          example: 1

        productSize:
          type: string
          enum:
          - 소형
          - 극소
          - 대형
          - 이형물
          - 중형
          - 특대
          description: 배송 물품의 사이즈
          example: 대형

        qrBarCode:
          type: string
          description: 바코드에 사용되는 코드. 보통 customerOrderId 값과 동일하게 입력하여도 무방함
          example: A1234567890

        senderBaseAddress:
          type: string
          description: 보내는 사람 주소
          example: 서울특별시 성동구 왕십리로 58

        senderDetailAddress:
          type: string
          description: 보내는 사람 상세주소
          example: 서울숲포휴 503호

        senderName:
          type: string
          description: 보내는 사람 이름
          example: 홍길동

        senderPhoneNumber:
          type: string
          description: 보내는 사람 연락처
          example: 010-1234-5678

        viaAddress:
          type: string
          description: 수신자의 기본 주소
          example: 서울특별시 강남구 삼성로 212

        viaDetailAddress:
          type: string
          description: 수신인의 상세 정보
          example: 24동 101호

        viaName:
          type: string
          description: 수신인 배송지 상호등 명칭. 상호명이 없을 경우 viaOwner와 동일하게 수신자 이름을 기입
          example: CU은마점

        viaNote:
          type: string
          description: 배송시의 메모 사항
          example: 현관문앞에 놔두세요

        viaOwner:
          type: string
          description: 수신인 이름
          example: 홍길동

        viaPhone:
          type: string
          description: 수신의 전화 번호
          example: 010-1234-5678

        viaType:
          type: string
          enum:
          - 방문지
          - 물류거점
          - 물류센터
          - 회차지점
          description: 배송의 종류 ( '방문지' 로 고정 합니다 )
          example: 방문지

        visitType:
          type: string
          enum:
          - 배송
          - 교환
          - 상차
          - 수거
          - 프리미엄
          - 하차
          - 회수
          description: 방문지 종류
          example: 배송
        warehouseCode:
          type: string
          description: 중간 입고지 명칭
          example: 강남HUB입고센터
        zipCode:
          type: string
          description: 담당 구역의 우편번호 (이미 우편번호가 존재시에 입력함)
          example: '06284'

    ResponseDeliveryOrder:
      title: ResponseDeliveryOrder
      type: object
      properties:
        customerOrderId:
          type: string
          description: 주문 번호
          example: "A1234567890"
        receiverName:
          type: string
          description: 배송지 명칭
          example: "CU은마점"
        productName:
          type: string
          description: 배송 물품
          example: "신선사과"
        deliveryRequestAt:
          type: string
          format: date-time
          description: 배송 요청 일시
          example: "2024-03-21T12:32:56"
        visitType:
          enum:
          - 배송
          - 교환
          - 상차
          - 수거
          - 프리미엄
          - 하차
          - 회수
          description: 배송 타입
          example: "배송"
        qrBarCode:
          type: string
          description: 배송 물품
          example: "A1234567890"
        projectName:
          type: string
          description: 프로젝트 이름
          example: "강남A-1회차배송"
        address:
          type: string
          description: 수신자 주소
          example: "서울특별시 강남구 삼성로 212"
      required:
      - customerOrderId
      - receiverName
      - productName
      - deliveryRequestAt
      - visitType
      - qrBarCode
      - projectName
      - address

    OrderItemDTO:
      type: object
      properties:
        itemCode:
          type: string
          description: 상품코드
          example: "7894324"
        productBarcode:
          type: string
          description: 물류코드
          example: "10-78"
        itemName:
          type: string
          description: 상품명
          example: "한우 등심 혼합"
        orderQuantity:
          type: integer
          description: 상품수량
          example: 1
          
      required:
        - itemCode
        - productBarcode
        - itemName
