apiVersion: apps/v1
kind: Deployment
metadata:
  name: lbs-swagger
  namespace: swagger
  labels:
    app: lbs-swagger
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lbs-swagger
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: lbs-swagger
    spec:
      containers:
      - image: harbor.logisteq.com/lbs-core/swagger:20240906-171458
        imagePullPolicy: Always
        name: lbs-swagger
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: lbs-swagger

      imagePullSecrets:
      - name: harbor-secret

      restartPolicy: Always


