apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: lbs-swagger
  namespace: swagger
  labels:
    app: lbs-swagger
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - lbs-swagger.cartamobility.com
  rules:
    - host: lbs-swagger.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: lbs-swagger
                port:
                  number: 80
