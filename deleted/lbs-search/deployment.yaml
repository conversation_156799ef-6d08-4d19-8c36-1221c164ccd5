apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: lbs-search
    kind: lbs-core
    environment: dev
  name: lbs-search
spec:  
  replicas: 1
  selector:
    matchLabels:
      app: lbs-search
  strategy:
    type: Recreate

  template:
    metadata:
      labels:
        app: lbs-search
        kind: lbs-core
        environment: dev
    spec:
      imagePullSecrets:
      - name: harbor-secret
      containers:
      - envFrom:
        - configMapRef:
            name: lbs-search
        name: lbs-search
        image: harbor.logisteq.com/lbs-core/search:latest
        imagePullPolicy: IfNotPresent

        ports:
        - containerPort: 15002
        - containerPort: 15012
        volumeMounts:
        - mountPath: /logs/search
          name: host-log-path

        livenessProbe:
          httpGet:
            path: /version
            port: 15002
          initialDelaySeconds: 20
          periodSeconds: 10

        resources:
          limits:
            cpu: 2000m
            memory: 5000Mi
          requests:
            cpu: 300m
            memory: 500Mi


      volumes:
      - name: host-log-path
        hostPath:
          path: /logs/search

