apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: lbs-route-global
  name: lbs-route-global
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lbs-route-global
  strategy:
    type: Recreate

  template:
    metadata:
      labels:
        app: lbs-route-global
        kind: lbs-core
    spec:
      nodeSelector:
        global: enable

      imagePullSecrets:
        - name: harbor-secret
      containers:
        - envFrom:
            - configMapRef:
                name: lbs-route-global
          name: lbs-route-global
          image: harbor.logisteq.com/lbs-core/global/route:20250416-163821
          imagePullPolicy: IfNotPresent

          ports:
            - containerPort: 15003            
          volumeMounts:
            - mountPath: /logs/debug/route
              name: host-log-path
            - mountPath: /appdata/KOR/LBS
              name: global-data

          livenessProbe:
            httpGet:
              path: /version
              port: 15003
            initialDelaySeconds: 30
            periodSeconds: 20
            failureThreshold: 5

          resources:
            limits:
              cpu: 2000m
              memory: 5000Mi
            requests:
              cpu: 300m
              memory: 500Mi

      volumes:
        - name: host-log-path
          hostPath:
            path: /logs/route-global
        - name: global-data
          hostPath:
            path: /appdata/globaldata
