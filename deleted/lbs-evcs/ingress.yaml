apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: evcs-ingress

  labels:
    app: lbs-evcs
    kind: lbs-core
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - charging.cartamobility.com
    - hosts:
        - charging.logisteq.com
      secretName: logisteq-secret-old
  rules:
    - host: charging.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: lbs-evcs
                port:
                  number: 16001
    - host: charging.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: lbs-evcs
                port:
                  number: 16001
