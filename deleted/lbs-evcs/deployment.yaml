apiVersion: apps/v1
kind: Deployment
metadata:
  
  labels:
    app: lbs-evcs
    kind: lbs-core
    environment: dev
  name: lbs-evcs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lbs-evcs
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: lbs-evcs
        kind: lbs-core
        environment: dev
    spec:
      nodeSelector:
        apptype: lbs
      imagePullSecrets:
      - name: harbor-secret
      containers:
      - env:
        - name: TZ
          value: Asia/Seoul
        - name: SERVICE_KEY
          value: "L2fs5P5Qu8b91PPGeoH%2BhllGWXa67zN314xgD9yGMyadkCU7pWBBM8jo4b%2BE0TQj%2FyRrQVOJAXPiYH3Gty%2FiJw%3D%3D"

        name: lbs-evcs
        image: harbor.logisteq.com/lbs-core/dev/evcs:20240819-221644
        imagePullPolicy: IfNotPresent

        ports:
        - containerPort: 16001

        volumeMounts:
        - mountPath: /logs/debug/evcsgatherer
          name: host-log-evcsgatherer
        - mountPath: /logs/debug/evcsserver
          name: host-log-evcsserver
        - mountPath: /appdata/evcharger
          name: host-appdata

        livenessProbe:
          httpGet:
            path: /
            port: 16001
          initialDelaySeconds: 20
          periodSeconds: 10

        resources:
          limits:
            cpu: 2000m
            memory: 2000Mi
          requests:
            cpu: 300m
            memory: 500Mi


      volumes:
      - name: host-log-evcsgatherer
        hostPath:
          path: /logs/debug/evcsgatherer
      - name: host-log-evcsserver
        hostPath:
          path: /logs/debug/evcsserver
      - name: host-appdata
        hostPath:
          path: /appdata/evcharger

