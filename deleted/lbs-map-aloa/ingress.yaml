apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: aloa-map

  labels:
    app: aloa-map
    kind: lbs-core
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - map-tiles-qa.cartamobility.com
    - hosts:
        - map-tiles-qa.logisteq.com
      secretName: logisteq-secret-old
  rules:
    - host: map-tiles-qa.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: aloa-map
                port:
                  number: 15000
    - host: map-tiles-qa.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: aloa-map
                port:
                  number: 15000
