apiVersion: apps/v1
kind: Deployment
metadata:
  
  labels:
    app: amoa-map
    kind: lbs-core
    environment: dev
  name: amoa-map
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: amoa-map
  template:
    metadata:
      labels:
        app: amoa-map
        kind: lbs-core
        environment: dev
    spec:
      nodeSelector:
        apptype: lbs
      containers:
      - name: amoa-map
        env:
        - name: TZ
          value: Asia/Seoul
        image: harbor.logisteq.com/lbs-core/dev/amoa-map:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 15011
        volumeMounts:
        - mountPath: /appdata/webmap_kr_amoa
          name: amoa-map-data
        - mountPath: /logs/debug/map
          name: lbs-logs
        resources:
          limits:
            cpu: 1000m
            memory: 5000Mi
          requests:
            cpu: 100m
            memory: 100Mi
      imagePullSecrets:
      - name: harbor-secret
      volumes:
      - name: amoa-map-data
        hostPath:
          path: /appdata/webmap_kr_amoa
      - name: lbs-logs
        hostPath:
          path: /logs/amoa-map