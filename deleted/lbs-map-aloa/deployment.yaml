apiVersion: apps/v1
kind: Deployment
metadata:
  
  labels:
    app: aloa-map
    kind: lbs-core
    environment: dev
  name: aloa-map
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: aloa-map
  template:
    metadata:
      labels:
        app: aloa-map
        kind: lbs-core
        environment: dev
    spec:
      nodeSelector:
        apptype: lbs
      containers:
      - name: aloa-map
        env:
        - name: TZ
          value: Asia/Seoul
        image: harbor.logisteq.com/lbs-core/dev/aloa-map:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 15000
        volumeMounts:
        - mountPath: /appdata/webmap_kr_aloa
          name: aloa-map-data
        - mountPath: /logs/debug/map
          name: lbs-logs
        resources:
          limits:
            cpu: 1000m
            memory: 5000Mi
          requests:
            cpu: 100m
            memory: 100Mi
      imagePullSecrets:
      - name: harbor-secret
      volumes:
      - name: aloa-map-data
        hostPath:
          path: /appdata/webmap_kr_aloa
      - name: lbs-logs
        hostPath:
          path: /logs/aloa-map