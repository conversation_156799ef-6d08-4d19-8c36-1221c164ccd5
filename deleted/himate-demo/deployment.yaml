apiVersion: apps/v1
kind: Deployment
metadata:
  name: himate-demo-web
  labels:
    app: himate-demo-web
spec:
  replicas: 1
  selector:
    matchLabels:
      app: himate-demo-web
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: himate-demo-web
    spec:
      nodeSelector:
        apptype: lbs
      imagePullSecrets:
        - name: harbor-secret
      containers:
        - image: harbor.logisteq.com/himate/demo/himate-web:20250509-135256
          imagePullPolicy: IfNotPresent
          name: himate-demo-web
          resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "128Mi"
              cpu: "100m"
          ports:
            - containerPort: 3000
          livenessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 10
      restartPolicy: Always
