apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: himate-demo-web
  labels:
    app: himate-demo-web
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    #nginx.ingress.kubernetes.io/whitelist-source-range: "***************/32,***************/32,***************/32"
spec:
  tls:
    - hosts:
        - forklift-fms-demo.cartamobility.com
    - hosts:
        - fms-demo.logisteq.com
      secretName: logisteq-secret-old
  rules:
    - host: forklift-fms-demo.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: himate-demo-web
                port:
                  number: 3000
    - host: fms-demo.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: himate-demo-web
                port:
                  number: 3000
