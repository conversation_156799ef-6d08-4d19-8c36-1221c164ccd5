apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: map-ingress

  labels:
    app: lbs-map
    kind: lbs-core
    environment: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  tls:
    - hosts:
        - map-tile2-qa-global.cartamobility.com
    - hosts:
        - map-tile2-qa-global.logisteq.com
      secretName: logisteq-secret-old
  rules:
    - host: map-tile2-qa-global.cartamobility.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: lbs-map-svc
                port:
                  number: 18000
    - host: map-tile2-qa-global.logisteq.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: lbs-map-svc
                port:
                  number: 18000