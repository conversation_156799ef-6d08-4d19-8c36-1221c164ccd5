apiVersion: apps/v1
kind: Deployment
metadata:
  
  labels:
    app: lbs-map
    kind: lbs-core
    environment: dev
  name: lbs-map
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: lbs-map
  template:
    metadata:
      labels:
        app: lbs-map
        kind: lbs-core
        environment: dev
    spec:
      nodeSelector:
        apptype: lbs
      containers:
      - name: lbs-map
        env:
        - name: TZ
          value: Asia/Seoul
        image: harbor.logisteq.com/lbs-core/dev/aloa-map2:20250415-094749
        imagePullPolicy: Always
        ports:
        - containerPort: 18000
        volumeMounts:
        - mountPath: /appdata/webmap_aloa2
          name: lbs-map-data
        - mountPath: /logs/debug/map
          name: lbs-logs
        resources:
          limits:
            cpu: 1000m
            memory: 5000Mi
          requests:
            cpu: 100m
            memory: 100Mi
      imagePullSecrets:
      - name: harbor-secret
      volumes:
      - name: lbs-map-data
        hostPath:
          path: /appdata/webmap_aloa2
      - name: lbs-logs
        hostPath:
          path: /logs/debug/map