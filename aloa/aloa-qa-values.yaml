# Default values for aloa.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

spring:
  profile: qa
  jpa:
    hibernate:
      ddlAuto: update

ingress:
  host: aloa-qa.cartamobility.com
  sub:
    host: aloa-qa.logisteq.com
    secretName: logisteq-secret-old
aloa:
  arch:
    proxyServer:
      url: https://aloa-qa.cartamobility.com/
  fcm:
    topicPrefixOfServer: qa
  system:
    privacy:
      external:
        url: http://************:3001 
  
aws:
  accessKey: ********************
  secretKey: lmsil8cxPuJkqfFd58XPLiX1Q8M0V5kecW/YT4CH
  bucketName: tms-file
  privateBucketName: tms-file-private


datasource:
  tms:
    type: mariadb
    url: ********************************
    username: logisteq_qa
    password: logisteq9090
    secretKey: Logisteq9090!!!!
    database:
      tms: tms_qa
      common: aloa_common_qa
      auth: aloa_auth_qa
      stat: tms_qa_stat
  track:
    type: mariadb
    url: ********************************
    username: logisteq_qa
    password: logisteq9090
    secretKey: Logisteq9090!!!!
    database:
      track: tms_track_qa


image:
  pullSecrets: harbor-secret
  imagePullPolicy: IfNotPresent
  tmsServiceTag: latest
  commonServiceTag: latest
  serviceRegisterTag: latest
  apiGatewayTag: latest
  tmsStatServiceTag: latest
  trackServiceTag: latest
  authServiceTag: latest
  tmsStatBatchTag: latest
  adminBackendTag: latest
  adminFrontendTag: latest

tmsService:
  replicas: 1
  maxReplicas: 1
  maximumPoolSize: 10
  service:
    port: 5800
  image:
    repository: harbor.logisteq.com/aloa/qa/tms-service
    tag: qa_20250531_0915

commonService:
  maximumPoolSize: 10
  service:
    port: 5810

  image:
    repository: harbor.logisteq.com/aloa/qa/common-service
    tag: latest


serviceRegister:
  service:
    port: 8761
    nodePort: 31081
  image:
    repository: harbor.logisteq.com/aloa/qa/service-register
    tag: latest


apiGateway:
  service:
    port: 7070
    nodePort: 31083
  image:
    repository: harbor.logisteq.com/aloa/qa/api-gateway
    tag: qa_20250529_0213


tmsStatService:
  service:
    port: 5802
    nodePort: 31084
  image:
    repository: harbor.logisteq.com/aloa/qa/tms-stat-service
    tag: qa_20250529_0837


trackService:
  maximumPoolSize: 10
  service:
    port: 5801
    nodePort: 31085
  image:
    repository: harbor.logisteq.com/aloa/qa/track-service
    tag: qa_20250529_0211


authService:
  service:
    port: 5903    
    nodePort: 31086
  image:
    repository: harbor.logisteq.com/aloa/qa/auth-service
    tag: qa_20250529_0213


tmsStatBatch:
  image:
    repository: harbor.logisteq.com/aloa/qa/tms-stat-batch
    tag: latest
  cron: "0 17 * * *"

trackRedis:
  service:
    port: 6379
    nodePort: 31087
  image:
    repository: redis
    tag: 6.0.16

webSessionRedis:
  service:
    port: 6379
    nodePort: 31088
  image:
    repository: redis
    tag: 6.0.16


emqxMaster:
  service:
    nodePort: 31179    
    tcp:
      NodePort: 31180
  image:
    repository: harbor.logisteq.com/aloa/emqx-master
    tag: latest



lbsProxy:
  service:
    port: 3003
  image:
    repository: harbor.logisteq.com/lbs-core/lbs-proxy
    tag: 20250523-223202
  port: "3003"

  search:
    url: "http://lbs-core-dev.logisteq.com:15002"
    weights: "1"
  route:
    url: "http://lbs-core-dev.logisteq.com:15003,http://lbs-core-dev.logisteq.com:16003"
    weights: "2,1"
  meter:
    url: "http://lbs-core-dev.logisteq.com:15003"
    weights: "1"
  dispatch:
    url: "http://lbs-core-dev.logisteq.com:15004"
    weights: "1"
  map:
    url: "https://map-tile2-qa.logisteq.com"
    weights: "1"

sms:
  solapi:
    apiSecret: DNN3M2NIWPXJUQDXLADF9DBGFSOLSCER

enableLmdAdminPage: false
