# Default values for aloa.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

spring:
  profile: dev
  jpa:
    hibernate:
      ddlAuto: update

ingress:
  host: aloa-dev.cartamobility.com
  sub:
    host: aloa-dev.logisteq.com
    secretName: logisteq-secret-old
aloa:
  arch:
    proxyServer:
      url: https://aloa-dev.cartamobility.com/
  fcm:
    topicPrefixOfServer: dev
  system:
    privacy:
      external:
        url: http://************:3001 
  
aws:
  accessKey: ********************
  secretKey: lmsil8cxPuJkqfFd58XPLiX1Q8M0V5kecW/YT4CH
  bucketName: tms-file
  privateBucketName: tms-file-private


datasource:
  tms:
    type: mariadb
    url: ********************************
    username: logisteq_dev
    password: logisteq9090
    secretKey: Logisteq9090!!!!
    database:
      tms: tms_dev
      common: aloa_common_dev
      auth: aloa_auth_dev
      stat: tms_dev_stat
  track:
    type: mariadb
    url: ********************************
    username: logisteq_dev
    password: logisteq9090
    secretKey: Logisteq9090!!!!
    database:
      track: tms_track_dev

image:
  pullSecrets: harbor-secret
  imagePullPolicy: IfNotPresent

tmsService:
  replicas: 1
  maxReplicas: 1
  maximumPoolSize: 10
  service:
    port: 5800
    nodePort: 31070
  image:
    repository: harbor.logisteq.com/aloa/dev/tms-service
    tag: dev_20250605_1053



commonService:
  maximumPoolSize: 10
  service:
    port: 5810
    nodePort: 31072
  image:
    repository: harbor.logisteq.com/aloa/dev/common-service
    tag: dev_20250523_2021


serviceRegister:
  service:
    port: 8761
    nodePort: 31071
  image:
    repository: harbor.logisteq.com/aloa/dev/service-register
    tag: dev_20241228_0026


apiGateway:
  service:
    port: 7070
    nodePort: 31073
  image:
    repository: harbor.logisteq.com/aloa/dev/api-gateway
    tag: dev_20241227_2353


tmsStatService:
  service:
    port: 5802
    nodePort: 31074
  image:
    repository: harbor.logisteq.com/aloa/dev/tms-stat-service
    tag: dev_20241228_0005


trackService:
  maximumPoolSize: 10
  service:
    port: 5801
    nodePort: 31075
  image:
    repository: harbor.logisteq.com/aloa/dev/track-service
    tag: dev_20241227_2347


authService:
  service:
    port: 5903
    nodePort: 31076
  image:
    repository: harbor.logisteq.com/aloa/dev/auth-service
    tag: dev_20250519_1510


tmsStatBatch:
  image:
    repository: harbor.logisteq.com/aloa/dev/tms-stat-batch
    tag: dev_20241231_1444
  cron: "0 17 * * *"

trackRedis:
  service:
    port: 6379
    nodePort: 31077
  image:
    repository: redis
    tag: 6.0.16

webSessionRedis:
  service:
    port: 6379
    nodePort: 31078
  image:
    repository: redis
    tag: 6.0.16


emqxMaster:
  service:
    nodePort: 31079    
    tcp:
      NodePort: 31080
  image:
    repository: harbor.logisteq.com/aloa/emqx-master
    tag: latest



lbsProxy:
  service:
    port: 3003
  image:
    repository: harbor.logisteq.com/lbs-core/lbs-proxy
    tag: 20250531-233617
  port: "3003"

  search:
    url: "http://lbs-core-dev.logisteq.com:15002"
    weights: "1"
  route:
    url: "http://lbs-core-dev.logisteq.com:15003,http://lbs-core-dev.logisteq.com:16003"
    weights: "2,1"
  meter:
    url: "http://lbs-core-dev.logisteq.com:15003"
    weights: "1"
  dispatch:
    url: "http://lbs-core-dev.logisteq.com:15004"
    weights: "1"
  map:
    url: "https://map-tile2-qa.logisteq.com"
    weights: "1"

sms:
  solapi:
    apiSecret: DNN3M2NIWPXJUQDXLADF9DBGFSOLSCER

enableLmdAdminPage: false
