apiVersion: apps/v1
kind: Deployment
metadata:
  name: common-service
  labels:
    app: common-service
    environment: {{ .Values.spring.profile }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: common-service
  template:
    metadata:
      labels:
        app: common-service
    spec:
      imagePullSecrets:
      - name: {{ .Values.image.pullSecrets }}
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 50
              preference:
                matchExpressions:
                  - key: nodename
                    operator: NotIn
                    values:
                      - main01
      containers:
      - name: common-service
        image: "{{ .Values.commonService.image.repository }}:{{ coalesce .Values.commonService.image.tag .Values.image.commonServiceTag "latest" }}"
        imagePullPolicy: {{ .Values.image.imagePullPolicy }}
        ports:
        - containerPort: 5810
        envFrom:
        - configMapRef:
            name: common-service
        volumeMounts:
        - name: common-service-log
          mountPath: /applog/common-service
        resources:
          limits:
            cpu: 1000m
            memory: 1000Mi
          requests:
            cpu: 500m
            memory: 500Mi
        livenessProbe:
          httpGet:
            path: /api/tms-core/search/version
            port: {{ .Values.commonService.service.port }}
          initialDelaySeconds: 60
          periodSeconds: 30
          successThreshold: 1
          failureThreshold: 3
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/tms-core/search/version
            port: {{ .Values.commonService.service.port }}
          initialDelaySeconds: 60
          periodSeconds: 20
          successThreshold: 1
          failureThreshold: 1
          timeoutSeconds: 10
        lifecycle:
          preStop:
            exec:
              command: ["sleep", "30"]
      volumes:
      - name: common-service-log
        hostPath:
          path: /logs/log-common-service-{{ .Release.Namespace }}
          type: DirectoryOrCreate
