apiVersion: v1
kind: Service
metadata:
  name: common-service
  labels:
    app: common-service
    environment: {{ .Values.spring.profile }}
spec:
  type: NodePort
  selector:
    app: common-service
  ports:
  - name: "common-service-port"
    port: {{ .Values.commonService.service.port }}
    targetPort: {{ .Values.commonService.service.port }}
    {{- if .Values.commonService.service.nodePort }}
    nodePort: {{ .Values.commonService.service.nodePort }}
    {{- end}}
