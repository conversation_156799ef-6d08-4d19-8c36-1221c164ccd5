apiVersion: v1
kind: Service
metadata:
  labels:
    app: emqx-master
  name: emqx-master
spec:
  type: NodePort
  ports:
  - name: "wss"
    port: 8083
    targetPort: 8083
    {{- if .Values.emqxMaster.service.nodePort }}
    nodePort: {{ .Values.emqxMaster.service.nodePort }}
    {{- end }}
  - name: "18083"
    port: 18083
    targetPort: 18083
  - name: "tcp"
    port: 1883
    targetPort: 1883
    {{- if .Values.emqxMaster.service.tcp.NodePort }}
    nodePort: {{ .Values.emqxMaster.service.tcp.NodePort }}
    {{- end }}
  selector:
    app: emqx-master
