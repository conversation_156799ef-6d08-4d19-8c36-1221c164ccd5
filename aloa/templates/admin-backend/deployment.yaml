{{- if .Values.enableLmdAdminPage }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-backend
  labels:
    app: admin-backend
    environment: {{ .Values.spring.profile }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: admin-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: admin-backend
    spec:
      containers:
      - name: admin-backend
        image: "{{ .Values.adminBackend.image.repository }}:{{ coalesce .Values.adminBackend.image.tag .Values.image.adminBackendTag "latest" }}"
        imagePullPolicy: {{ .Values.image.imagePullPolicy }}
        ports:
        - containerPort: {{ .Values.adminBackend.service.port }}
        envFrom:
        - configMapRef:
            name: admin-backend
        resources:
          limits:
            cpu: 500m
            memory: 5000Mi
          requests:
            cpu: 100m
            memory: 200Mi

{{end}}