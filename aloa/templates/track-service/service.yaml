apiVersion: v1
kind: Service
metadata:
  name: track-service
  labels:
    app: track-service
    environment: {{ .Values.spring.profile }}
spec:
  type: NodePort
  selector:
    app: track-service
  ports:
    - name: "track-service-port"
      port: {{ .Values.trackService.service.port }}
      targetPort: {{ .Values.trackService.service.port }}
      {{- if .Values.trackService.service.nodePort }}
      nodePort: {{ .Values.trackService.service.nodePort }}
      {{- end }}
