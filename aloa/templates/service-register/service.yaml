apiVersion: v1
kind: Service
metadata:
  name: service-register
  labels:
    app: service-register
    environment: {{ .Values.spring.profile }}
spec:
  selector:
    app: service-register
  type: NodePort
  ports:
    - name: "eureka-port"
      port: {{ .Values.serviceRegister.service.port }}
      targetPort: {{ .Values.serviceRegister.service.port }}
      {{- if .Values.serviceRegister.service.nodePort }}
      nodePort: {{ .Values.serviceRegister.service.nodePort }}
      {{- end }}
