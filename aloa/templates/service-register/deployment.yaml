apiVersion: apps/v1
kind: Deployment
metadata:
  name: service-register
  labels:
    app: service-register
    environment: {{ .Values.spring.profile }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: service-register
  template:

    metadata:
      labels:
        app: service-register
    spec:
      imagePullSecrets:
      - name: {{ .Values.image.pullSecrets }}
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 50
              preference:
                matchExpressions:
                  - key: nodename
                    operator: NotIn
                    values:
                      - main01
      containers:
        - name: service-register
          image: "{{ .Values.serviceRegister.image.repository }}:{{ coalesce .Values.serviceRegister.image.tag .Values.image.serviceRegisterTag "latest" }}"
          imagePullPolicy: {{ .Values.image.imagePullPolicy }}
          ports:
            - containerPort: {{ .Values.serviceRegister.service.port }}
          volumeMounts:
            - name: service-register-log
              mountPath: /applog/service-register
          envFrom:
            - configMapRef:
                name: service-register
      volumes:
        - name: service-register-log
          hostPath:
            path: /logs/log-service-register-{{ .Release.Namespace }}
            type: DirectoryOrCreate
