apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  labels:
    app: api-gateway
    environment: {{ .Values.spring.profile }}
spec:
  type: NodePort
  selector:
    app: api-gateway
  ports:
    - name: "gateway-port"
      port: {{ .Values.apiGateway.service.port }}
      targetPort: {{ .Values.apiGateway.service.port }}
      {{- if .Values.apiGateway.service.nodePort }}
      nodePort: {{ .Values.apiGateway.service.nodePort }}
      {{- end}}
