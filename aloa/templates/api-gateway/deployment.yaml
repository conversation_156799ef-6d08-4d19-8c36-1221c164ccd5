apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  labels:
    app: api-gateway
    environment: {{ .Values.spring.profile }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      imagePullSecrets:
      - name: {{ .Values.image.pullSecrets }}
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 50
              preference:
                matchExpressions:
                  - key: nodename
                    operator: NotIn
                    values:
                      - main01
      
      containers:
        - name: api-gateway
          image: "{{ .Values.apiGateway.image.repository }}:{{ coalesce .Values.apiGateway.image.tag .Values.image.apiGatewayTag "latest" }}"
          imagePullPolicy: {{ .Values.image.imagePullPolicy }}
          ports:
            - containerPort: {{ .Values.apiGateway.service.port }}
          envFrom:
            - configMapRef:
                name: api-gateway
          resources:
            limits:
              cpu: 1000m
              memory: 5000Mi
            requests:
              cpu: 200m
              memory: 800Mi
          volumeMounts:
            - name: api-gateway-log
              mountPath: /applog/api-gateway

      volumes:
        - name: api-gateway-log
          hostPath:
            path: /logs/log-api-gateway-{{ .Release.Namespace }}
            type: DirectoryOrCreate
