apiVersion: apps/v1
kind: Deployment
metadata:
  name: lbs-proxy
  labels:
    app: lbs-proxy
spec:
  replicas: {{ .Values.lbsProxy.replicas }}
  selector:
    matchLabels:
      app: lbs-proxy
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: lbs-proxy
    spec:

      imagePullSecrets:
      - name: harbor-secret
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: nodename
                operator: In
                values:
                - main01
      containers:
      - name: lbs-proxy
        image: "{{ .Values.lbsProxy.image.repository }}:{{ .Values.lbsProxy.image.tag }}"
        imagePullPolicy: {{ .Values.image.imagePullPolicy }}
        ports:
        - containerPort: {{ .Values.lbsProxy.service.port }}
        envFrom:
        - configMapRef:
            name: lbs-proxy
        volumeMounts:
        - name: lbs-proxy-log
          mountPath: /logs/lbs-proxy
        resources:
          limits:
            cpu: 500m
            memory: 5000Mi
          requests:
            cpu: 100m
            memory: 200Mi
        livenessProbe:
          httpGet:
            path: /health/liveness
            port: 3003
          initialDelaySeconds: 15
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health/readiness
            port: 3003
          initialDelaySeconds: 20
          periodSeconds: 10
        lifecycle:
          preStop:
            exec:
              command: ["sleep", "30"]
      volumes:
      - name: lbs-proxy-log
        hostPath:
          path: /logs-lbs-proxy-{{ .Release.Namespace }}
          type: DirectoryOrCreate
