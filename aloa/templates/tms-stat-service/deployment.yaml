apiVersion: apps/v1
kind: Deployment
metadata:
  name: tms-stat-service
  labels:
    app: tms-stat-service
    environment: {{ .Values.spring.profile }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tms-stat-service
  template:
    metadata:
      labels:
        app: tms-stat-service
    spec:
      imagePullSecrets:
      - name: {{ .Values.image.pullSecrets }}
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 50
              preference:
                matchExpressions:
                  - key: nodename
                    operator: NotIn
                    values:
                      - main01
      containers:
        - name: tms-stat-service
          image: "{{ .Values.tmsStatService.image.repository }}:{{ coalesce .Values.tmsStatService.image.tag .Values.image.tmsStatServiceTag "latest" }}"
          imagePullPolicy: {{ .Values.image.imagePullPolicy }}
          ports:
            - containerPort: {{ .Values.tmsStatService.service.port }}
          resources:
            limits:
              cpu: 1000m
              memory: 5000Mi
            requests:
              cpu: 200m
              memory: 300Mi
          envFrom:
            - configMapRef:
                name: tms-stat-service
          volumeMounts:
            - name: tms-stat-service-log
              mountPath: /applog/tms-stat-service

      volumes:
        - name: tms-stat-service-log
          hostPath:
            path: /logs/log-tms-stat-service-{{ .Release.Namespace }}
            type: DirectoryOrCreate
