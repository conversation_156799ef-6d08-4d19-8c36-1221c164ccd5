apiVersion: v1
kind: Service
metadata:
  name: tms-stat-service
  labels:
    app: tms-stat-service
    environment: {{ .Values.spring.profile }}
spec:
  type: NodePort
  selector:
    app: tms-stat-service
  ports:
    - name: "tms-stat-port"
      port: {{ .Values.tmsStatService.service.port }}
      targetPort: {{ .Values.tmsStatService.service.port }}
      {{- if .Values.tmsStatService.service.nodePort }}
      nodePort: {{ .Values.tmsStatService.service.nodePort }}
      {{- end }}
