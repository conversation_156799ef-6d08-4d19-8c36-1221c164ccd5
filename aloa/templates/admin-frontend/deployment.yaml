{{- if .Values.enableLmdAdminPage }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-frontend
  labels:
    app: admin-frontend
    environment: {{ .Values.spring.profile }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: admin-frontend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: admin-frontend
    spec:
      containers:
        - name: admin-backend
          image: "{{ .Values.adminFrontend.image.repository }}:{{ coalesce .Values.adminFrontend.image.tag .Values.image.adminFrontendTag "latest" }}"
          imagePullPolicy: {{ .Values.image.imagePullPolicy }}
          ports:
            - containerPort: {{ .Values.adminFrontend.service.port }}
          env:
            - name: TZ
              value: Asia/Seoul
        resources:
          limits:
            cpu: 500m
            memory: 5000Mi
          requests:
            cpu: 100m
            memory: 200Mi
{{ end }}

