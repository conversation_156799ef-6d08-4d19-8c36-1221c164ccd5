{{- if .Values.enableLmdAdminPage }}
apiVersion: v1
kind: Service
metadata:
  name: admin-frontend
  labels:
    app: admin-frontend
    environment: {{ .Values.spring.profile }}
spec:
  type: NodePort
  selector:
    app: admin-frontend
  ports:
  - name: "admin-frontend-port"
    port: {{ .Values.adminFrontend.service.port }}
    targetPort: {{ .Values.adminFrontend.service.port }}
    {{- if .Values.adminFrontend.service.nodePort }}
    nodePort: {{ .Values.adminFrontend.service.nodePort }}
    {{- end}}
{{ end }}