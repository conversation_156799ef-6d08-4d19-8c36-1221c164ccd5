apiVersion: v1
kind: Service
metadata:
  name: web-session-redis
  labels:
    app: web-session-redis
    environment: {{ .Values.spring.profile }}
spec:
  type: NodePort
  selector:
    app: web-session-redis
  ports:
    - name: "web-session-redis-port"
      port: {{ .Values.webSessionRedis.service.port }}
      targetPort: {{ .Values.webSessionRedis.service.port }}
      {{- if .Values.webSessionRedis.service.nodePort }}
      nodePort: {{ .Values.webSessionRedis.service.nodePort }}
      {{- end }}
