apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  labels:
    app: auth-service
    environment: {{ .Values.spring.profile }}

spec:
  replicas: 1
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      hostname: auth-service
      
      imagePullSecrets:
      - name: {{ .Values.image.pullSecrets }}

      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 50
              preference:
                matchExpressions:
                  - key: nodename
                    operator: NotIn
                    values:
                      - main01
      
      containers:
      - name: auth-service
        image: "{{ .Values.authService.image.repository }}:{{ coalesce .Values.authService.image.tag .Values.image.authServiceTag "latest" }}"
        imagePullPolicy: {{ .Values.image.imagePullPolicy }}
        ports:
        - containerPort: {{ .Values.authService.service.port }}
        envFrom:
        - configMapRef:
            name: auth-service
        resources:
          limits:
            cpu: 500m
            memory: 1000Mi
          requests:
            cpu: 100m
            memory: 500Mi
        volumeMounts:
        - name: auth-service-log
          mountPath: /applog/auth-service
      volumes:
      - name: auth-service-log
        hostPath:
          path: /logs/log-auth-service-{{ .Release.Namespace }}
          type: DirectoryOrCreate

