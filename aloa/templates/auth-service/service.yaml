apiVersion: v1
kind: Service
metadata:
  name: auth-service
  labels:
    app: auth-service
    environment: {{ .Values.spring.profile }}
spec:
  type: NodePort
  selector:
    app: auth-service
  ports:
  - name: "auth-service-port"
    port: {{ .Values.authService.service.port }}
    targetPort: {{ .Values.authService.service.port }}
    {{- if .Values.authService.service.nodePort }}
    nodePort: {{ .Values.authService.service.nodePort }}
    {{- end}}
