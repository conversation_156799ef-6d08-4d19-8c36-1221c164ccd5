apiVersion: v1
kind: Service
metadata:
  name: track-redis
  labels:
    app: track-redis
    environment: {{ .Values.spring.profile }}
spec:
  type: NodePort
  selector:
    app: track-redis
  ports:
    - name: "track-redis-port"
      port: {{ .Values.trackRedis.service.port }}
      targetPort: {{ .Values.trackRedis.service.port }}
      {{- if .Values.trackRedis.service.nodePort }}
      nodePort: {{ .Values.trackRedis.service.nodePort }}
      {{- end }}