apiVersion: v1
kind: Service
metadata:
  name: tms-service
  labels:
    app: tms-service
    environment: {{ .Values.spring.profile }}
spec:
  type: NodePort
  selector:
    app: tms-service
  ports:
    - name: "tms-service-port"
      port: {{ .Values.tmsService.service.port }}
      targetPort: {{ .Values.tmsService.service.port }}
      {{- if .Values.tmsService.service.nodePort }}
      nodePort: {{ .Values.tmsService.service.nodePort }}
      {{- end}}
