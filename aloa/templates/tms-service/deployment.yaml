apiVersion: apps/v1
kind: Deployment
metadata:
  name: tms-service
  labels:
    app: tms-service
    environment: {{ .Values.spring.profile }}
spec:
  replicas: {{ .Values.tmsService.replicas }}
  selector:
    matchLabels:
      app: tms-service
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1 # 의도한 파드 수에 대해 추가 생성할 수 있는 최대 파드의 수
      maxUnavailable: 0 # 롤링 업데이트 프로세스 중에 사용할 수 없는 최대 파드의 수 ##########
  template:
    metadata:
      labels:
        app: tms-service
    spec:
      imagePullSecrets:
        - name: {{ .Values.image.pullSecrets }}
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 50
              preference:
                matchExpressions:
                  - key: performance
                    operator: NotIn
                    values:
                      - slow
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 80
              preference:
                matchExpressions:
                  - key: nodename
                    operator: NotIn
                    values:
                      - main01
      containers:
        - name: tms-service
          image: "{{ .Values.tmsService.image.repository }}:{{ coalesce .Values.tmsService.image.tag .Values.image.tmsServiceTag "latest" }}"
          imagePullPolicy: {{ .Values.image.imagePullPolicy }}
          ports:
            - containerPort: {{ .Values.tmsService.service.port }}
          envFrom:
            - configMapRef:
                name: tms-service
          volumeMounts:
            - name: tms-service-log
              mountPath: /applog/tms-service
          resources:
            limits:
              cpu: 2500m
              memory: 5000Mi
            requests:
              cpu: 500m
              memory: 1500Mi
          livenessProbe:
            httpGet:
              path: /
              port: {{ .Values.tmsService.service.port }}
            initialDelaySeconds: 120
            periodSeconds: 30
            successThreshold: 1
            failureThreshold: 3
            timeoutSeconds: 10
          lifecycle:
            preStop:
              exec:
                command: ["sleep", "60"]
      volumes:
        - name: tms-service-log
          hostPath:
            path: /logs/logs-tms-service-{{ .Release.Namespace }}
            type: DirectoryOrCreate
