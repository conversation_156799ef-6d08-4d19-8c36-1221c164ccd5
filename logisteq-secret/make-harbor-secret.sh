kubectl create secret docker-registry -n lbs container-secret \
 --docker-server=container.logisteq.com \
  --docker-username=psh \
  --docker-password=Logisteq9090

kubectl create secret docker-registry -n itgo harbor-secret \
 --docker-server=harbor.logisteq.com \
  --docker-username=psh \
  --docker-password=Logisteq9090


  kubectl create secret docker-registry -n lbs harbor-secret \
 --docker-server=harbor.logisteq.com \
  --docker-username=psh \
  --docker-password=Logisteq9090

 kubectl create secret docker-registry -n swagger harbor-secret \
 --docker-server=harbor.logisteq.com \
  --docker-username=psh \
  --docker-password=Logisteq9090

 kubectl create secret docker-registry -n trucker-dev harbor-secret \
 --docker-server=harbor.logisteq.com \
  --docker-username=psh \
  --docker-password=Logisteq9090

 kubectl create secret docker-registry -n default harbor-secret \
 --docker-server=harbor.logisteq.com \
  --docker-username=psh \
  --docker-password=Logisteq9090

kubectl create secret docker-registry -n aloa-dev harbor-secret \
 --docker-server=harbor.logisteq.com \
  --docker-username=psh \
  --docker-password=Logisteq9090

kubectl create secret docker-registry -n aloa-qa harbor-secret \
 --docker-server=harbor.logisteq.com \
  --docker-username=psh \
  --docker-password=Logisteq9090


kubectl create secret docker-registry -n himate harbor-secret \
 --docker-server=harbor.logisteq.com \
  --docker-username=psh \
  --docker-password=Logisteq9090


  kubectl create secret docker-registry -n global harbor-secret \
 --docker-server=harbor.logisteq.com \
  --docker-username=psh \
  --docker-password=Logisteq9090