kubectl delete secret  logisteq-secret-old -n trucker
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n trucker

kubectl delete secret  logisteq-secret-old -n aloa
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n aloa

kubectl delete secret  logisteq-secret-old -n aloa-dev
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n aloa-dev

kubectl delete secret  logisteq-secret-old -n aloa-prod
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n aloa-prod

kubectl delete secret  logisteq-secret-old -n aloa-qa
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n aloa-qa

kubectl delete secret  logisteq-secret-old -n lbs
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n lbs

kubectl delete secret  logisteq-secret-old -n himate
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n himate

kubectl delete secret  logisteq-secret-old -n lmd
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n lmd

kubectl delete secret  logisteq-secret-old -n fms
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n fms

kubectl delete secret  logisteq-secret-old -n fms-dev
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n fms-dev

kubectl delete secret  logisteq-secret-old -n trucker-dev
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n trucker-dev

kubectl delete secret  logisteq-secret-old -n global
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n global

kubectl delete secret  logisteq-secret-old -n swagger
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n swagger

kubectl delete secret  logisteq-secret-old -n argocd
kubectl create secret tls logisteq-secret-old --key privkey.pem --cert fullchain.pem -n argocd