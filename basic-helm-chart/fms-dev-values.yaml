# Values for fms-dev environment, overriding basic-helm-chart defaults

appName: fms-dev # Added: Application name used in various templates

replicaCount: 1

image:
  repository: harbor.logisteq.com/carta/fms/fms-backend
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: 20250524-002203

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  
  ports: # Add this section to satisfy ingress template
    - type: NodePort
      port: 8080
      targetPort: 8080

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    # kubernetes.io/ingress.class: nginx # Usually handled by className in modern controllers
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: fms-dev-api.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
          # serviceName and servicePort are typically templated in the base chart
    - host: fms-dev-api.logisteq.com
      paths:
        - path: /
          pathType: Prefix
          # serviceName and servicePort are typically templated in the base chart
  tls:
    - hosts:
        - fms-dev-api.cartamobility.com
      # secretName: # Optional, if specific cert needed (e.g., cert-manager default)
    - hosts:
        - fms-dev-api.logisteq.com
      secretName: logisteq-secret-old

# Resource limits and requests
resources:
  limits:
    cpu: 2000m
    memory: 2048Mi
  requests:
    cpu: 250m
    memory: 256Mi

# Node affinity
affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 50
      preference:
        matchExpressions:
        - key: performance
          operator: NotIn
          values:
          - slow

# Volumes and Volume Mounts
volumes:
  - name: log-volume
    hostPath:
      path: /logs/fms-dev # WARNING: hostPath can be problematic and environment-specific. Consider alternatives like persistent volumes or sidecar log collectors.
      type: DirectoryOrCreate
volumeMounts:
  - name: log-volume
    mountPath: /applog

# ConfigMap data
# Note: Sensitive data like passwords/keys should ideally be managed via Kubernetes Secrets, not ConfigMaps.
configMap:
  enabled: true
  # name: # Templated in base chart
  data:
    TZ: "Asia/Seoul"
    SPRING_PROFILES_ACTIVE: "dev"
    SPRING_DATASOURCE_URL: "jdbc:mysql://************:9319/fms_dev?allowPublicKeyRetrieval=true&useSSL=false&serverTimezone=Asia/Seoul&characterEncoding=UTF-8"
    SPRING_DATASOURCE_USERNAME: "logisteq"
    SPRING_DATASOURCE_PASSWORD: "logisteq9090" # TODO: Migrate to Secret
    GLOBAL_DATABASE_SECRET-KEY: "CartaMobility!@#" # TODO: Migrate to Secret

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: /actuator/health/liveness # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 8080)
  initialDelaySeconds: 100
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
  timeoutSeconds: 10

# Readiness Probe (Enabled as per original deployment)
readinessProbe:
  enabled: true
  httpGet:
    path: /actuator/health/readiness # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 8080)
  initialDelaySeconds: 120
  periodSeconds: 20
  successThreshold: 1
  failureThreshold: 1
  timeoutSeconds: 10

# Additional labels for Pods
podLabels:
  environment: dev

# Name override (if needed, base chart usually handles this via fullname template)
# nameOverride: ""
# fullnameOverride: ""
