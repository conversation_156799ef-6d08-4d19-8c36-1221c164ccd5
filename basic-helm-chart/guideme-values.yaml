# Values for guidme environment, overriding basic-helm-chart defaults

appName: guideme

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/guideme
  pullPolicy: Always
  tag: "latest" # Original deployment used 'latest' tag

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  ports: # Add this section to satisfy ingress template
    - type: NodePort
      port: 16020
      targetPort: 16020

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: guideme.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
    - host: guideme.logisteq.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - guideme.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host
    - hosts:
        - guideme.logisteq.com
      secretName: logisteq-secret-old

# Resource limits and requests
resources:
  limits:
    cpu: 1000m
    memory: 5000Mi
  requests:
    cpu: 100m
    memory: 100Mi

# Node affinity (Not specified in original deployment)
# affinity: {}

# Volumes and Volume Mounts (Original used hostPath, not directly mapped in base chart values)
# volumes: []
# volumeMounts: []

# ConfigMap data
configMap:
  enabled: true
  data:
    TZ: "Asia/Seoul"
    DB_CONNECTION_LIMIT: "10"
    DB_HOST: "************"
    DB_PORT: "9318"
    DB_USER: "logisteq"
    DB_PASSWORD: "logisteq9090" # TODO: Migrate to Secret
    DB_NAME: "GUIDEME_DB"
    DB_DEBUG: "false"

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Liveness Probe (Not specified in original deployment)
livenessProbe:
  enabled: false

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  kind: lbs-core
  environment: dev
