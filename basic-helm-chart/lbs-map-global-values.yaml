# Values for lbs-map-global environment, overriding basic-helm-chart defaults

appName: lbs-map # Note: Original resources used 'lbs-map'

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/dev/aloa-map2 # Note: Image repo seems to be aloa-map2
  pullPolicy: Always # Original deployment used Always
  tag: "20250415-094749" # Tag from original deployment

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  ports: # Add this section to satisfy ingress template
    - type: ClusterIP # Original service type was default (ClusterIP)
      port: 18000
      targetPort: 18000

# Ingress definition
ingress:
  enabled: true
  # nameOverride: map-ingress # Optional: If the ingress name MUST be different from appName
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: map-tile2-qa-global.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
    - host: map-tile2-qa-global.logisteq.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - map-tile2-qa-global.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host
    - hosts:
        - map-tile2-qa-global.logisteq.com
      secretName: logisteq-secret-old

# Resource limits and requests
resources:
  limits:
    cpu: 1000m
    memory: 5000Mi
  requests:
    cpu: 100m
    memory: 100Mi

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector
nodeSelector:
  apptype: lbs # From original deployment

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: lbs-map-data # Renamed from original for clarity if needed, but keeping original name
    hostPath:
      path: /appdata/webmap_aloa2
      # type: DirectoryOrCreate # Type not specified
  - name: lbs-logs
    hostPath:
      path: /logs/debug/map # Path from original deployment
      # type: DirectoryOrCreate # Type not specified
volumeMounts:
  - name: lbs-map-data # Must match volume name
    mountPath: /appdata/webmap_aloa2 # Mount path from original deployment
  - name: lbs-logs
    mountPath: /logs/debug/map # Mount path from original deployment

# ConfigMap data (Converted from original deployment's 'env' section)
configMap:
  enabled: true
  data:
    TZ: "Asia/Seoul"

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Liveness Probe (Not specified in original deployment)
livenessProbe:
  enabled: false

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  kind: lbs-core
  environment: dev
