# Values for lbs-cluster environment, overriding basic-helm-chart defaults

appName: lbs-cluster

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/cluster
  pullPolicy: IfNotPresent # Original was IfNotPresent, changed from Always for consistency if needed
  tag: "latest" # Original deployment used 'latest' tag

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  ports: # Add this section to satisfy ingress template
    - type: NodePort
      port: 15004
      targetPort: 15004

# Ingress definition (Disabled as no ingress.yaml was present)
ingress:
  enabled: false
  # className: "nginx"
  # annotations: {}
  # hosts: []
  # tls: []

# Resource limits and requests (Not specified in original deployment)
# resources: {}

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector (Not specified in original deployment)
# nodeSelector: {}

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged in Helm charts due to portability issues.
# Consider PersistentVolumeClaims or other volume types if possible.
volumes:
  - name: host-log-path
    hostPath:
      path: /logs/cluster
      # type: DirectoryOrCreate # Type not specified in original
volumeMounts:
  - name: host-log-path
    mountPath: /app/lbs-core-cluster/logs

# ConfigMap data (Converted from original deployment's 'env' section)
configMap:
  enabled: true
  data:
    TZ: "Asia/Seoul"

# Deployment strategy (Using Recreate from original deployment)
strategy:
  type: Recreate
  # rollingUpdate: {} # Not applicable for Recreate strategy

# Liveness Probe (Not specified in original deployment)
livenessProbe:
  enabled: false

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  kind: lbs-core
  environment: dev
