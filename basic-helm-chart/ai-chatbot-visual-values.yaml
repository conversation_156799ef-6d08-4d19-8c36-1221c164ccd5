# Values for lbs-route environment, overriding basic-helm-chart defaults

appName: ai-chat-visual

replicaCount: 1

image:
  repository: harbor.logisteq.com/carta/ai-chatbot-visual
  pullPolicy: IfNotPresent
  tag: 20250606-234859

imagePullSecrets:
  - name: harbor-secret

# Service definition with multiple ports
service:
  type: NodePort
  ports:
    - name: http # Name from original service
      port: 8003
      targetPort: 8003
      protocol: TCP

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    # kubernetes.io/ingress.class: nginx # Usually handled by className in modern controllers
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "ALLOWALL" always;
      add_header Content-Security-Policy "frame-ancestors *" always;
  hosts:
    - host: aichat-visual.cartamobility.com
      paths:
        - path: /
          pathType: Prefix

  tls:
    - hosts:
        - aichat-visual.cartamobility.com


# Resource limits and requests
resources:
  limits:
    cpu: 5000m
    memory: 8000Mi
  requests:
    cpu: 300m
    memory: 500Mi



# ConfigMap data
configMap:
  enabled: true
  data:
    # 환경 설정
    TZ: "Asia/Seoul"
    ENVIRONMENT: "development"
    DEBUG: "true"
    
    # OpenAI 설정
    OPENAI_API_KEY: "********************************************************"
    SQL_QUERY_MODEL: "gpt-4o-mini"
    DATA_ANALYSIS_MODEL: "gpt-4o-mini"
    
    # MySQL 데이터베이스 설정
    MYSQL_HOST: "db-logis.logisteq.com"
    MYSQL_PORT: "9311"
    MYSQL_USER: "root"
    MYSQL_PASSWORD: "logisteq9090"
    MYSQL_DATABASE: "tms_dev_stat_test"
    
    # LangSmith 설정
    LANGCHAIN_TRACING_V2: "true"
    LANGSMITH_API_KEY: "***************************************************"
    LANGSMITH_PROJECT: "text_to_sql_app"
    
    # DynamoDB 설정
    DYNAMODB_ENDPOINT_URL: "http://************:8000"
    AWS_REGION: "ap-northeast-2"
    AWS_ACCESS_KEY_ID: "********************"
    AWS_SECRET_ACCESS_KEY: "YG9bCEYDk87Nf7awDKEHCH6eX+k0QidkErCpAFli"
    DYNAMODB_SESSIONS_TABLE: "chat_sessions"
    DYNAMODB_MESSAGES_TABLE: "chat_messages"
    
    # AWS S3 설정
    S3_BUCKET_NAME: "carta-fms-public"
    S3_BUCKET_PREFIX: "charts/"
    S3_ENDPOINT_URL: ""
    S3_PUBLIC_READ: "true"
    S3_PRESIGNED_URL_EXPIRY: "3600"
    S3_MAX_FILE_SIZE: "10485760"
    
    # 서버 및 세션 관리 설정
    SERVER_ID: "k8s-dev"
    SERVER_DOMAIN: "aichat-app.cartamobility.com"
    SESSION_TTL_DAYS: "30"
    MAX_SESSIONS_PER_SERVER: "100"
    MESSAGE_RETENTION_DAYS: "90"
    
    # 성능 및 캐시 설정
    SCHEMA_CACHE_ENABLED: "true"
    SCHEMA_CACHE_TTL: "3600"
    
    # API 서버 설정
    API_HOST: "0.0.0.0"
    API_PORT: "8002"
    API_RELOAD: "false"
    
    # 시각화 서비스 설정
    VISUALIZATION_API_URL: "https://aichat-visual.cartamobility.com"
    
    # 허용된 테이블 설정 (보안)
    ALLOWED_TABLES: "stat_general_daily_delivery,stat_general_monthly_delivery,stat_general_total_delivery,stat_general_weekly_delivery,stat_general_yearly_delivery,stat_group_name_daily_delivery,stat_group_name_daily_project_delivery,stat_group_name_monthly_delivery,stat_group_name_total_delivery,stat_group_name_weekly_delivery,stat_group_name_yearly_delivery,stat_project_delivery,stat_rider_daily_delivery,stat_rider_daily_project_delivery,stat_rider_monthly_delivery,stat_rider_total_delivery,stat_rider_weekly_delivery,stat_rider_yearly_delivery"


# Deployment strategy (Using Recreate from original deployment)
strategy:
  type: Recreate
  # rollingUpdate: {} # Not applicable for Recreate strategy

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: false
  httpGet:
    path: /health # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 15003)
  initialDelaySeconds: 40
  periodSeconds: 30
  # failureThreshold: # Not specified in original

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

