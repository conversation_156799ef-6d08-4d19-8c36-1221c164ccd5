apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.appName }}
  labels:
    helm.sh/chart: {{ include "chart.chart" . }}
    app.kubernetes.io/name: {{ include "chart.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    {{- if .Chart.AppVersion }}
    app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
    {{- end }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  {{- with .Values.strategy }}
  strategy:
{{ toYaml . | indent 4 }}
  {{- end }}
  selector:
    matchLabels:
      # Use the selector labels helper directly here for consistency
      app: {{ .Values.appName }} # Changed from 'name' to 'app' to match service selector
      
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
{{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        # Use the selector labels helper directly here for consistency
        app: {{ .Values.appName }} # Changed from 'name' to 'app' to match service selector
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
{{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.podSecurityContext }}
      securityContext:
{{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Values.appName }}
          {{- with .Values.securityContext }}
          securityContext:
{{- toYaml . | nindent 12 }}
          {{- end }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.configMap.enabled }}
          envFrom:
            - configMapRef:
                name: {{ .Values.appName }} # Use appName for consistency as requested
          {{- end }}
          ports:
          {{- range .Values.service.ports }}
            - name: {{ .name | default "http" }}
              containerPort: {{ .targetPort }}
              protocol: {{ .protocol | default "TCP" }}
          {{- end }}
          {{- with .Values.lifecycle }}
          lifecycle:
{{- toYaml . | nindent 12 }}
          {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.livenessProbe.httpGet.path }}
              port: http
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            {{- with .Values.livenessProbe.timeoutSeconds }}
            timeoutSeconds: {{ . }}
            {{- end }}
            {{- with .Values.livenessProbe.failureThreshold }}
            failureThreshold: {{ . }}
            {{- end }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.readinessProbe.httpGet.path }}
              port: http
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            {{- with .Values.readinessProbe.timeoutSeconds }}
            timeoutSeconds: {{ . }}
            {{- end }}
            {{- with .Values.readinessProbe.failureThreshold }}
            failureThreshold: {{ . }}
            {{- end }}
          {{- end }}
          {{- with .Values.resources }}
          resources:
{{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
{{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.volumes }}
      volumes:
{{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
{{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
{{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
{{- toYaml . | nindent 8 }}
      {{- end }}
