apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.appName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.service.ports }}
    - name: {{ .name | default "http" }}
      port: {{ .port }}
      targetPort: {{ .targetPort }}
      protocol: {{ .protocol | default "TCP" }}
      {{- if (and (eq $.Values.service.type "NodePort") .nodePort) }} # Use $.Values.service.type to access top-level value inside range
      nodePort: {{ .nodePort }}
      {{- end }}
    {{- end }}
  selector:
    app: {{ .Values.appName }}
