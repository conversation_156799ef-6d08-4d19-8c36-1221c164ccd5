# Values for lbs-swagger environment, overriding basic-helm-chart defaults

# Note: Original manifests specified namespace 'swagger'.
# The base chart needs to handle namespace definition, or it must be applied during deployment.
appName: lbs-swagger

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/swagger
  pullPolicy: Always # Original deployment used Always
  tag: "20240906-171458"

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  type: NodePort
  ports:
    - name: http # Assuming default name for port 80
      port: 80
      targetPort: 8080 # Container port from original deployment
      protocol: TCP
      nodePort: 30083 # Explicitly set NodePort from original service

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: lbs-swagger.cartamobility.com
      paths:
        - path: /
          pathType: Prefix

    - host: lbs-swagger.logisteq.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - lbs-swagger.cartamobility.com
    - hosts:
        - lbs-swagger.logisteq.com
      secretName: logisteq-secret-old
# Resource limits and requests (Not specified in original deployment)
# resources: {}

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector (Not specified in original deployment)
# nodeSelector: {}

# Volumes and Volume Mounts (Not specified in original deployment)
# volumes: []
# volumeMounts: []

# ConfigMap data
configMap:
  enabled: true
  data:
    URLS: '[{ url: ''./docs/swagger.yaml'', name: ''lbs'' }]'
    URLS_PRIMARY_NAME: lbs-core

# Deployment strategy
strategy:
  type: Recreate # Original deployment used Recreate strategy

# Liveness Probe (Not specified in original deployment)
livenessProbe:
  enabled: false

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  app: lbs-swagger # Label from original deployment selector/template
