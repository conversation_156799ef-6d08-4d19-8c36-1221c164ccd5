# Values for himate-demo environment, overriding basic-helm-chart defaults

appName: himate-demo-web # Note: Original resources used 'himate-demo-web'

replicaCount: 1

image:
  repository: harbor.logisteq.com/himate/demo/himate-web
  pullPolicy: IfNotPresent
  tag: 20250523-141143

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  ports: # Add this section to satisfy ingress template
    - type: NodePort
      port: 3000
      targetPort: 3000

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    # nginx.ingress.kubernetes.io/whitelist-source-range: "***************/32,***************/32,***************/32" # Commented out as in original
  hosts:
    - host: forklift-fms-demo.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
    - host: fms-demo.logisteq.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - forklift-fms-demo.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host
    - hosts:
        - fms-demo.logisteq.com
      secretName: logisteq-secret-old

# Resource limits and requests
resources:
  limits:
    memory: "512Mi"
    cpu: "500m"
  requests:
    memory: "128Mi"
    cpu: "100m"

# Node affinity
affinity: {} # Clear default affinity from base chart if not needed
nodeSelector:
  apptype: lbs # From original deployment

# Volumes and Volume Mounts (Not specified in original deployment)
# volumes: []
# volumeMounts: []

# ConfigMap data (Not present in original Kustomize setup)
configMap:
  enabled: false
  # data: {}

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: / # Path from original deployment
    port: http # Template uses named port 'http'
  initialDelaySeconds: 30 # Adjusted from original (was 60)
  periodSeconds: 10 # Adjusted from original (was 30)
  failureThreshold: 3

# Readiness Probe (Enabled as per original deployment)
readinessProbe:
  enabled: true
  httpGet:
    path: / # Path from original deployment
    port: http # Template uses named port 'http'
  initialDelaySeconds: 5
  periodSeconds: 10
  failureThreshold: 3

# Additional labels for Pods (Taken from original metadata)
# podLabels: {} # No extra labels in original deployment metadata
