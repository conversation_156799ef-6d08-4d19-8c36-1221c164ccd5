# Values for trucker-prod environment, overriding basic-helm-chart defaults

appName: fms-service # Note: Original resources used 'fms-service'

replicaCount: 1

image:
  repository: harbor.logisteq.com/trucker/prod/fms-service
  pullPolicy: IfNotPresent
  tag: "prod_20250411_2152" # Tag from prod deployment

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  type: NodePort
  ports:
    - name: http # Assuming default name 'http' for port 8080
      port: 8080
      targetPort: 8080
      protocol: TCP
      # nodePort: # Not specified in original service

# Ingress definition
ingress:
  enabled: true
  # nameOverride: fms-service # Optional: Ingress name matches appName by default
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: fms.cartamobility.com # Prod host
      paths:
        - path: /
          pathType: Prefix
    - host: fms.logisteq.com # Prod host
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - fms.cartamobility.com # Prod host
      # secretName: # Original ingress did not specify a secretName for this host
    - hosts:
        - fms.logisteq.com # Prod host
      secretName: logisteq-secret-old

# Resource limits and requests
resources:
  limits:
    memory: "512Mi"
    cpu: "500m"
  requests:
    memory: "256Mi"
    cpu: "250m"

# Node affinity
affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 50
        preference:
          matchExpressions:
            - key: performance
              operator: NotIn
              values:
                - slow

# Node selector (Not specified in original deployment)
# nodeSelector: {}

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: log-volume
    hostPath:
      path: /logs/trucker-prod # Prod log path
      type: DirectoryOrCreate # Type specified in original
volumeMounts:
  - name: log-volume
    mountPath: /applog

# ConfigMap data
configMap:
  enabled: true
  data:
    TZ: "Asia/Seoul"
    SPRING_PROFILES_ACTIVE: "prod" # Prod profile
    SPRING_DATASOURCE_URL: "*****************************************" # Prod DB URL
    SPRING_DATASOURCE_USERNAME: "logisteq"
    SPRING_DATASOURCE_PASSWORD: "Logisteq9090!!!!" # Prod DB password - TODO: Migrate to Secret
    GLOBAL_DATABASE_SECRET-KEY: "CartaMobility!@#" # TODO: Migrate to Secret
    SPRING_JPA_HIBERNATE_DDL-AUTO: "update" # Specific to prod config

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: /monitor/health/liveness # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 8080)
  initialDelaySeconds: 100
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
  timeoutSeconds: 10

# Readiness Probe (Enabled as per original deployment)
readinessProbe:
  enabled: true
  httpGet:
    path: /monitor/health/readiness # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 8080)
  initialDelaySeconds: 120
  periodSeconds: 20
  successThreshold: 1
  failureThreshold: 1
  timeoutSeconds: 10

# Additional labels for Pods (Taken from Ingress metadata)
podLabels:
  environment: prod
