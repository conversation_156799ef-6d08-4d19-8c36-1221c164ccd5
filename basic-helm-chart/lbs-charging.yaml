# Values for lbs-evcs environment, overriding basic-helm-chart defaults

appName: lbs-evcs

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/dev/evcs
  pullPolicy: IfNotPresent
  tag: "20240819-221644"

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  ports: # Add this section to satisfy ingress template
    - type: NodePort
      port: 16001
      targetPort: 16001

# Ingress definition
ingress:
  enabled: true
  # nameOverride: evcs-ingress # Optional: If the ingress name MUST be different from appName
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: charging.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
    - host: charging.logisteq.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - charging.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host
    - hosts:
        - charging.logisteq.com
      secretName: logisteq-secret-old

# Resource limits and requests
resources:
  limits:
    cpu: 2000m
    memory: 2000Mi
  requests:
    cpu: 300m
    memory: 500Mi

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector
nodeSelector:
  apptype: lbs # From original deployment

# Volumes and Volume Mounts (Using multiple hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: host-log-evcsgatherer
    hostPath:
      path: /logs/debug/evcsgatherer
      # type: DirectoryOrCreate # Type not specified
  - name: host-log-evcsserver
    hostPath:
      path: /logs/debug/evcsserver
      # type: DirectoryOrCreate # Type not specified
  - name: host-appdata
    hostPath:
      path: /appdata/evcharger
      # type: DirectoryOrCreate # Type not specified
volumeMounts:
  - name: host-log-evcsgatherer
    mountPath: /logs/debug/evcsgatherer
  - name: host-log-evcsserver
    mountPath: /logs/debug/evcsserver
  - name: host-appdata
    mountPath: /appdata/evcharger

# ConfigMap data (Converted from original deployment's 'env' section)
configMap:
  enabled: true
  data:
    TZ: "Asia/Seoul"
    SERVICE_KEY: "L2fs5P5Qu8b91PPGeoH%2BhllGWXa67zN314xgD9yGMyadkCU7pWBBM8jo4b%2BE0TQj%2FyRrQVOJAXPiYH3Gty%2FiJw%3D%3D" # TODO: Migrate to Secret

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: / # Path from original deployment
    port: http # Template uses named port 'http'
  initialDelaySeconds: 20
  periodSeconds: 10
  # failureThreshold: # Not specified in original

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  kind: lbs-core
  environment: dev
