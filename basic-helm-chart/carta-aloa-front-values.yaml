# Values for carta-aloa-front environment, overriding basic-helm-chart defaults

appName: carta-aloa-front

replicaCount: 1

image:
  repository: harbor.logisteq.com/carta/aloa-frontend
  pullPolicy: IfNotPresent
  tag: 20250605-215040

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  type: NodePort
  ports:
    - name: http
      port: 3000
      targetPort: 3000
      
  

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: aloa-new-dev.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
    - host: aloa-new-dev.logisteq.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - aloa-new-dev.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host
    - hosts:
        - aloa-new-dev.logisteq.com
      secretName: logisteq-secret-old

# Resource limits and requests (Not specified in original deployment)
# resources: {}
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

# Node affinity
affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 50
        preference:
          matchExpressions:
            - key: performance
              operator: NotIn
              values:
                - slow

# Volumes and Volume Mounts (Not specified in original deployment)
# volumes: []
# volumeMounts: []

# ConfigMap data (Converted from original deployment's 'env' section)
configMap:
  enabled: true
  data:
    HOST: "0.0.0.0"
    NUXT_HOST: "0.0.0.0"
    NUXT_PORT: "3000" # Note: This is usually the container port, check if needed as env var

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Liveness Probe (Not specified in original deployment)
livenessProbe:
  enabled: false

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from Ingress metadata)
podLabels:
  environment: dev
