# Values for aloa-swagger environment, overriding basic-helm-chart defaults

# Note: Original manifests specified namespace 'swagger'.
# The base chart needs to handle namespace definition, or it must be applied during deployment.
appName: aloa-swagger

replicaCount: 1

image:
  repository: swaggerapi/swagger-ui # Public image
  pullPolicy: IfNotPresent # Or Always, depending on desired behavior for untagged images
  tag: latest # Uses chart default (appVersion) or 'latest' if chart default not set

# imagePullSecrets: [] # Override default harbor-secret as this is a public image
imagePullSecrets: [] # Explicitly empty list

# Service definition
service:
  type: NodePort
  ports:
    - name: http # Assuming default name for port 80
      port: 80
      targetPort: 8080 # Container port from original deployment
      protocol: TCP
      nodePort: 30082 # Explicitly set NodePort from original service

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: swagger.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
    - host: swagger.logisteq.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - swagger.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host
    - hosts:
        - swagger.logisteq.com
      secretName: logisteq-secret-old

# Resource limits and requests
resources:
  limits:
    memory: "512Mi"
    cpu: "500m"
  requests:
    memory: "128Mi"
    cpu: "100m"

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector
nodeSelector:
  apptype: lbs # From original deployment

# Volumes and Volume Mounts
# WARNING: hostPath with absolute paths is not portable. Consider alternatives.
# WARNING: Mounting specific ConfigMap keys might require adjustments to the base chart template.
volumes:
  - name: swagger-yaml
    hostPath:
      # Path from original deployment - highly environment-specific!
      path: /home/<USER>/dev-k8s-cfgstore/swagger/aloa/docs
      type: DirectoryOrCreate
  - name: aloa-swagger # Define the volume that references the ConfigMap
    configMap:
      name: aloa-swagger # Name of the ConfigMap resource to mount from
  # Volume for nginx.conf is implicitly the main ConfigMap if mounted via volumeMounts referencing configMapKeyRef
  # - name: nginx-conf # This volume definition might be handled differently by the base chart
  #   configMap:
  #     name: {{ .Values.appName }} # Assumes base chart uses appName for ConfigMap
  #     items:
  #       - key: nginx.conf
  #         path: nginx.conf

volumeMounts:
  - name: swagger-yaml # Mount the hostPath volume
    mountPath: /usr/share/nginx/html/docs
  - name: aloa-swagger # Mount the configMap volume defined above
    mountPath: /etc/nginx/nginx.conf # Target path inside container
    subPath: nginx.conf # The key within the ConfigMap to mount

# ConfigMap data
configMap:
  enabled: true
  data:
    URLS: '[{ url: ''./docs/swagger.yaml'', name: ''logisteq'' }]'
    URLS_PRIMARY_NAME: logisteq
    # Content of nginx.conf from original ConfigMap
    nginx.conf: |
      user  nginx;
      worker_processes  1;

      error_log  /var/log/nginx/error.log notice;
      pid        /var/run/nginx.pid;
      events {
          worker_connections  1024;
      }
      http {
          include       /etc/nginx/mime.types;
          default_type  application/octet-stream;
          charset       utf-8;

          log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                            '$status $body_bytes_sent "$http_referer" '
                            '"$http_user_agent" "$http_x_forwarded_for"';

          access_log  /var/log/nginx/access.log  main;
          sendfile        on;
          #tcp_nopush     on;
          keepalive_timeout  65;
          #gzip  on;
          include /etc/nginx/conf.d/*.conf;
          server {

            location /docs/swagger.yaml {
                charset utf-8; # 해당 위치의 문자 인코딩을 UTF-8로 설정
                types {
                  application/yaml yaml; # MIME 타입 설정
                }
            }


          }
      }

# Deployment strategy
strategy:
  type: Recreate # Original deployment used Recreate strategy

# Liveness Probe (Not specified in original deployment)
livenessProbe:
  enabled: false

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  app: aloa-swagger # Label from original deployment selector/template
