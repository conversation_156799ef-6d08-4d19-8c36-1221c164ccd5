# Values for carta-admin-web environment, overriding basic-helm-chart defaults

appName: aichat-web

replicaCount: 1

image:
  repository: harbor.logisteq.com/carta/aichat-web
  pullPolicy: IfNotPresent
  tag: 20250606-123641

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  type: NodePort
  ports: # Add this section to satisfy ingress template
    - port: 3000
      targetPort: 3000

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: assistant.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - assistant.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host

# Resource limits and requests
resources:
  limits:
    memory: "512Mi"
    cpu: "500m"
  requests:
    memory: "256Mi"
    cpu: "250m"

# Node affinity (Not specified in original deployment)
# affinity: {}

# Volumes and Volume Mounts (Not specified in original deployment)
# volumes: []
# volumeMounts: []

# ConfigMap data (Not present in original Kustomize setup)
configMap:
  enabled: true
  data:
    NEXT_PUBLIC_API_BASE_URL: "https://aichat-app.cartamobility.com"

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: / # Path from original deployment
    port: http # Template uses named port 'http'
  initialDelaySeconds: 100
  periodSeconds: 60
  successThreshold: 1
  failureThreshold: 3
  timeoutSeconds: 10

# Readiness Probe (Disabled, as not present in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from Ingress metadata)
podLabels:
  environment: dev
