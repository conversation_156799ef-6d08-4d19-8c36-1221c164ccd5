# Values for lbs-route environment, overriding basic-helm-chart defaults

appName: ai-chatbot

replicaCount: 1

image:
  repository: harbor.logisteq.com/carta/ai-chatbot
  pullPolicy: IfNotPresent
  tag: 20250521-115633

imagePullSecrets:
  - name: harbor-secret

# Service definition with multiple ports
service:
  type: NodePort
  ports:
    - name: http # Name from original service
      port: 8501
      targetPort: 8501
      protocol: TCP

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    # kubernetes.io/ingress.class: nginx # Usually handled by className in modern controllers
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "ALLOWALL" always;
      add_header Content-Security-Policy "frame-ancestors *" always;
  hosts:
    - host: aichat.cartamobility.com
      paths:
        - path: /
          pathType: Prefix

  tls:
    - hosts:
        - aichat.cartamobility.com


# Resource limits and requests
resources:
  limits:
    cpu: 5000m
    memory: 8000Mi
  requests:
    cpu: 300m
    memory: 500Mi


# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: host-log-path
    hostPath:
      path: /logs/aichatbot
      # type: DirectoryOrCreate # Type not specified
volumeMounts:
  - name: host-log-path
    mountPath: /logs/aichatbot

# ConfigMap data
configMap:
  enabled: true
  data:
    TZ: "Asia/Seoul"
    OPENAI_API_KEY: "********************************************************"
    MYSQL_HOST: "************"
    MYSQL_PORT: "9311"
    MYSQL_USER: "root"
    MYSQL_PASSWORD: "logisteq9090"
    MYSQL_DATABASE: "tms_dev"
    LANGCHAIN_TRACING_V2: "true"
    LANGSMITH_API_KEY: "***************************************************"
    LANGSMITH_PROJECT: "text_to_sql_app"
    SQL_QUERY_MODEL: "gpt-4o-mini"
    DATA_ANALYSIS_MODEL: "gpt-4o-mini"
    ALLOWED_TABLES: "rider,delivery,rider_info,delivery_allocation,delivery_detail"

# Deployment strategy (Using Recreate from original deployment)
strategy:
  type: Recreate
  # rollingUpdate: {} # Not applicable for Recreate strategy

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: / # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 15003)
  initialDelaySeconds: 40
  periodSeconds: 30
  # failureThreshold: # Not specified in original

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

