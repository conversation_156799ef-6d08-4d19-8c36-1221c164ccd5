# Values for carta-aloa-backend environment, overriding basic-helm-chart defaults

appName: carta-aloa-backend

replicaCount: 1

image:
  repository: harbor.logisteq.com/carta/aloa-api
  pullPolicy: IfNotPresent
  tag: 20250605-152108

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  ports: # Add this section to satisfy ingress template
    - type: NodePort
      port: 8080
      targetPort: 8080

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity, though className is preferred
    nginx.ingress.kubernetes.io/proxy-body-size: "2g" 
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: aloa-new-dev-api.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - aloa-new-dev-api.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host

# Resource limits and requests
resources:
  limits:
    cpu: 2000m
    memory: 5000Mi
  requests:
    cpu: 300m
    memory: 500Mi

# Node affinity
affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 50
        preference:
          matchExpressions:
            - key: performance
              operator: NotIn
              values:
                - slow

# Volumes and Volume Mounts
volumes:
  - name: host-log-path
    hostPath:
      path: /logs/carta-aloa-backend # WARNING: hostPath usage.
      # type: DirectoryOrCreate # Original deployment didn't specify type
volumeMounts:
  - name: host-log-path
    mountPath: /logs/

# ConfigMap data
configMap:
  enabled: true
  data:
    SPRING_PROFILES_ACTIVE: dev
    TZ: Asia/Seoul
    HOST_VMS-URL: https://bmea-api.logisteq.com
    HOST_VMS-TOKEN: eyJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.uwQPMY2ZQWLuFWLvM7dGvaAfJjqIaI52dufw3-_63aY # TODO: Migrate to Secret
    SPRING_DATASOURCE_URL: *********************************************** # TODO: Migrate credentials to Secret

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: /actuator/health/liveness # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 8080)
  initialDelaySeconds: 100
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
  timeoutSeconds: 3

# Readiness Probe (Enabled as per original deployment)
readinessProbe:
  enabled: true
  httpGet:
    path: /actuator/health/readiness # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 8080)
  initialDelaySeconds: 120
  periodSeconds: 20
  successThreshold: 1
  failureThreshold: 1
  timeoutSeconds: 2

# Readiness Probe (Disabled, as not present in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from Ingress metadata)
podLabels:
  environment: dev
