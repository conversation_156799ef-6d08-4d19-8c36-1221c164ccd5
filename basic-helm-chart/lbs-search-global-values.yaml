# Values for lbs-search-global environment, overriding basic-helm-chart defaults

appName: lbs-search-global

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/global/search
  pullPolicy: IfNotPresent
  tag: 20250529-163713

imagePullSecrets:
  - name: harbor-secret

# Service definition with multiple ports
service:
  type: NodePort
  ports:
    - name: http # Name from original service
      port: 15002
      targetPort: 15002
      protocol: TCP
      nodePort: 31602 # Explicitly set NodePort from original service
    - name: native-port # Name from original service
      port: 15012
      targetPort: 15012
      protocol: TCP # Assuming TCP, not specified in original container ports
      nodePort: 31612 # Explicitly set NodePort from original service

# Ingress definition (Disabled as no ingress.yaml was present)
ingress:
  enabled: false
  # className: "nginx"
  # annotations: {}
  # hosts: []
  # tls: []

# Resource limits and requests
resources:
  limits:
    cpu: 2000m
    memory: 5000Mi
  requests:
    cpu: 300m
    memory: 500Mi

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector
nodeSelector:
  global: enable # From original deployment

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: host-log-path
    hostPath:
      path: /logs/search-global
      # type: DirectoryOrCreate # Type not specified
  - name: global-data
    hostPath:
      path: /appdata/globaldata
      # type: DirectoryOrCreate # Type not specified
volumeMounts:
  - name: host-log-path
    mountPath: /logs/debug/search
  - name: global-data
    mountPath: /appdata/search_core_kr/GogoRoot_KOR_TMS

# ConfigMap data
configMap:
  enabled: true
  data:
    TZ: Asia/Seoul
    SECRET: TMS_Search_Server # TODO: Migrate to Secret
    SEARCH_ENGINE_TARGET: global

# Deployment strategy (Using Recreate from original deployment)
strategy:
  type: Recreate
  # rollingUpdate: {} # Not applicable for Recreate strategy

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: /version # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 15002)
  initialDelaySeconds: 30
  periodSeconds: 10
  failureThreshold: 4

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  kind: lbs-core
  environment: dev
