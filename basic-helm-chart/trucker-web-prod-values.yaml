# Values for trucker-web-prod environment, overriding basic-helm-chart defaults

appName: trucker-web-prod

replicaCount: 1

image:
  repository: harbor.logisteq.com/trucker/prod/trucker-web
  pullPolicy: IfNotPresent
  tag: "20250415-190112" # Tag from prod deployment

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  type: NodePort
  ports:
    - name: http # Assuming default name 'http' for port 3000
      port: 3000
      targetPort: 3000
      protocol: TCP
      # nodePort: # Not specified in original service

# Ingress definition
ingress:
  enabled: true
  # nameOverride: trucker-web-prod # Optional: Ingress name matches appName by default
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: trucker-web.cartamobility.com # Prod host
      paths:
        - path: /
          pathType: Prefix
    - host: trucker-web.logisteq.com # Prod host
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - trucker-web.cartamobility.com # Prod host
      # secretName: # Original ingress did not specify a secretName for this host
    - hosts:
        - trucker-web.logisteq.com # Prod host
      secretName: logisteq-secret-old # Secret name from prod ingress

# Resource limits and requests
resources:
  limits:
    memory: "512Mi"
    cpu: "500m"
  requests:
    memory: "256Mi"
    cpu: "250m"

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector (Not specified in original deployment)
# nodeSelector: {}

# Volumes and Volume Mounts (Not specified in original deployment)
# volumes: []
# volumeMounts: []

# ConfigMap data
configMap:
  enabled: true
  data:
    TZ: "Asia/Seoul"
    GOOGLE_CLIENT_ID: "895751795663-hh0r7hjqsudvgqtimhn09j61ou825vut.apps.googleusercontent.com"
    GOOGLE_CLIENT_SECRET: "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" # TODO: Migrate to Secret
    NEXTAUTH_SECRET: "MyNextAuthSecretKEY12345" # TODO: Migrate to Secret
    BACKEND_URL: "https://fms.logisteq.com" # Prod backend URL
    NEXTAUTH_URL: "https://trucker-web.logisteq.com" # Prod NextAuth URL

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Not specified in original deployment)
livenessProbe:
  enabled: false

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from Ingress metadata)
podLabels:
  environment: prod
