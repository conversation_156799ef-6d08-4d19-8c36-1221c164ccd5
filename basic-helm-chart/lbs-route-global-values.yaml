# Values for lbs-route-global environment, overriding basic-helm-chart defaults

appName: lbs-route-global

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/global/route
  pullPolicy: IfNotPresent
  tag: "20250416-163821"

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  type: NodePort
  ports:
    - name: route-port # Name from original service
      port: 15003
      targetPort: 15003
      protocol: TCP
      nodePort: 31603 # Explicitly set NodePort from original service

# Ingress definition (Disabled as no ingress.yaml was present)
ingress:
  enabled: false
  # className: "nginx"
  # annotations: {}
  # hosts: []
  # tls: []

# Resource limits and requests
resources:
  limits:
    cpu: 2000m
    memory: 5000Mi
  requests:
    cpu: 300m
    memory: 500Mi

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector
nodeSelector:
  global: enable # From original deployment

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: host-log-path
    hostPath:
      path: /logs/route-global
      # type: DirectoryOrCreate # Type not specified
  - name: global-data
    hostPath:
      path: /appdata/globaldata
      # type: DirectoryOrCreate # Type not specified
volumeMounts:
  - name: host-log-path
    mountPath: /logs/debug/route
  - name: global-data
    mountPath: /appdata/KOR/LBS

# ConfigMap data
configMap:
  enabled: true
  data:
    TZ: Asia/Seoul
    SECRET: TMS_Route_Server # TODO: Migrate to Secret
    SEARCH_ENGINE_TARGET: global

# Deployment strategy (Using Recreate from original deployment)
strategy:
  type: Recreate
  # rollingUpdate: {} # Not applicable for Recreate strategy

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: /version # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 15003)
  initialDelaySeconds: 30
  periodSeconds: 20
  failureThreshold: 5

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  kind: lbs-core
