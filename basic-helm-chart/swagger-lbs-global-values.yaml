appName: lbs-global-swagger

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/global-swagger
  pullPolicy: Always # Original deployment used Always
  tag: 20250524-014853

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  type: NodePort
  ports:
    - name: http # Assuming default name for port 80
      port: 80
      targetPort: 8080 # Container port from original deployment
      protocol: TCP
      

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: lbs-swagger-global.cartamobility.com
      paths:
        - path: /
          pathType: Prefix

  tls:
    - hosts:
        - lbs-swagger-global.cartamobility.com

# Resource limits and requests (Not specified in original deployment)
# resources: {}

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector (Not specified in original deployment)
# nodeSelector: {}

# Volumes and Volume Mounts (Not specified in original deployment)
# volumes: []
# volumeMounts: []

# ConfigMap data
configMap:
  enabled: true
  data:
    URLS: '[{ url: ''./docs/swagger.yaml'', name: ''lbs'' }]'
    URLS_PRIMARY_NAME: lbs-core

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Liveness Probe (Not specified in original deployment)
livenessProbe:
  enabled: false

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false
