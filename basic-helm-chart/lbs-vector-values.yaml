# Values for lbs-vector environment, overriding basic-helm-chart defaults

appName: lbs-vector

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/vector
  pullPolicy: IfNotPresent
  tag: "latest" # Original deployment used 'latest' tag

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  type: NodePort
  ports:
    - name: http # Name from original service port definition
      port: 16000
      targetPort: 16000
      protocol: TCP
      nodePort: 31060 # Corrected capitalization and value from original service

# Ingress definition (Disabled as no ingress.yaml was present)
ingress:
  enabled: false
  # className: "nginx"
  # annotations: {}
  # hosts: []
  # tls: []

# Resource limits and requests
resources:
  limits:
    memory: "1024Mi"
    cpu: "500m"
  requests:
    memory: "256Mi"
    cpu: "100m"

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector
nodeSelector:
  apptype: lbs # From original deployment

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: lbs-vector-claim
    hostPath:
      path: /appdata/vector_data
      type: DirectoryOrCreate # Type specified in original
  - name: lbs-vector-log
    hostPath:
      path: /logs/vectorservice
      type: DirectoryOrCreate # Type specified in original
volumeMounts:
  - name: lbs-vector-claim
    mountPath: /appdata/vector_data
  - name: lbs-vector-log
    mountPath: /logs/debug/vectorservice

# ConfigMap data (Not present in original Kustomize setup)
configMap:
  enabled: false
  # data: {}

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: /version # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 16000)
  initialDelaySeconds: 30
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
  timeoutSeconds: 10

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (None specified in original deployment metadata)
# podLabels: {}
