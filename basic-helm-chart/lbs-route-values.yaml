# Values for lbs-route environment, overriding basic-helm-chart defaults

appName: lbs-route

replicaCount: 5

image:
  repository: harbor.logisteq.com/lbs-core/route
  pullPolicy: IfNotPresent
  tag: "20250107-035541"

imagePullSecrets:
  - name: harbor-secret

# Service definition with multiple ports
service:
  type: NodePort
  ports:
    - name: http # Name from original service
      port: 15003
      targetPort: 15003
      protocol: TCP
      nodePort: 31503 # Explicitly set NodePort from original service
    - name: native-port # Name from original service
      port: 20304
      targetPort: 20304
      protocol: TCP # Assuming TCP, not specified in original container ports
      nodePort: 31703 # Explicitly set NodePort from original service

# Ingress definition (Disabled as no ingress.yaml was present)
ingress:
  enabled: false
  # className: "nginx"
  # annotations: {}
  # hosts: []
  # tls: []

# Resource limits and requests
resources:
  limits:
    cpu: 5000m
    memory: 8000Mi
  requests:
    cpu: 300m
    memory: 500Mi

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector (Not specified in original deployment)
# nodeSelector: {}

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: host-log-path
    hostPath:
      path: /logs/route
      # type: DirectoryOrCreate # Type not specified
volumeMounts:
  - name: host-log-path
    mountPath: /logs/route

# ConfigMap data
configMap:
  enabled: true
  data:
    TZ: "Asia/Seoul"
    KUBERNATES_RUN: "true"
    PM2_CLUSTER_COUNT: "1"

# Deployment strategy (Using Recreate from original deployment)
strategy:
  type: Recreate
  # rollingUpdate: {} # Not applicable for Recreate strategy

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: /version # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 15003)
  initialDelaySeconds: 40
  periodSeconds: 30
  # failureThreshold: # Not specified in original

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  kind: lbs-core
  environment: dev
