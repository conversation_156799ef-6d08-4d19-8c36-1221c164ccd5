# Values for lbs-proxy environment, overriding basic-helm-chart defaults

appName: lbs-proxy-global

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/global/lbs-proxy
  pullPolicy: IfNotPresent
  tag: 20250523-223427

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  
  ports:
    - type: ClusterIP # Original service type was default (ClusterIP)
      port: 3003
      targetPort: 3003

# Ingress definition
ingress:
  enabled: true
  # nameOverride: lbs-proxy # Optional: Ingress name matches appName by default
  className: "nginx"
  annotations: {} # No specific annotations in original ingress
  hosts:
    - host: lbs-proxy-global.cartamobility.com
    
      paths:
        - path: /
          pathType: Prefix

  tls:
    - hosts:
        - lbs-proxy-global.cartamobility.com


# Resource limits and requests
resources:
  limits:
    memory: "9000Mi"
    cpu: "500m"
  requests:
    memory: "256Mi"
    cpu: "250m"

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector (Not specified in original deployment)
# nodeSelector: {}

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: lbs-proxy-log
    hostPath:
      path: /logs-lbs-proxy
      type: DirectoryOrCreate # Type specified in original
volumeMounts:
  - name: lbs-proxy-log
    mountPath: /applog/lbs-proxy

# ConfigMap data
configMap:
  enabled: true
  data:
    PORT: "3003"
    API_BASE_URL: "https://lbs-proxy-global.logisteq.com"
    MAP_URL: "https://map-tile2-qa.logisteq.com"
    SEARCH_SERVERS: "http://lbs-search-global.lbs.svc.cluster.local:15002"
    ROUTE_SERVERS: "http://lbs-route-global.lbs.svc.cluster.local:15003"
    METER_SERVERS: "http://aloa-core1.logisteq.com:15001"
    DISPATCH_SERVERS: "http://lbs-core-dev.logisteq.com:15004"
    MAP_SERVERS: "https://map-tile2-qa.logisteq.com"
    SEARCH_WEIGHTS: "1,1,1,1,1"
    ROUTE_WEIGHTS: "1,1,1,1,3,1,1"
    METER_WEIGHTS: "1"
    DISPATCH_WEIGHTS: "1"
    MAP_WEIGHTS: "1"

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Container lifecycle hooks
lifecycle:
  preStop:
    exec:
      command: ["sleep", "30"]

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: /health/liveness # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 3003)
  initialDelaySeconds: 15
  periodSeconds: 30
  # failureThreshold: # Not specified in original

# Readiness Probe (Enabled as per original deployment)
readinessProbe:
  enabled: true
  httpGet:
    path: /health/readiness # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 3003)
  initialDelaySeconds: 20
  periodSeconds: 10
  # failureThreshold: # Not specified in original

# Additional labels for Pods (None specified in original deployment metadata)
# podLabels: {}
