# Values for lbs-map-aloa environment, overriding basic-helm-chart defaults

appName: aloa-map # Note: Original resources used 'aloa-map'

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/dev/aloa-map
  pullPolicy: Always # Original deployment used Always
  tag: "latest" # Original deployment used 'latest' tag

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  
  ports: # Add this section to satisfy ingress template
    - type: NodePort
      port: 15000
      targetPort: 15000

# Ingress definition
ingress:
  enabled: true
  # nameOverride: aloa-map # Optional: If the ingress name MUST be different from appName
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: map-tiles-qa.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
    - host: map-tiles-qa.logisteq.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - map-tiles-qa.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host
    - hosts:
        - map-tiles-qa.logisteq.com
      secretName: logisteq-secret-old

# Resource limits and requests
resources:
  limits:
    cpu: 1000m
    memory: 5000Mi
  requests:
    cpu: 100m
    memory: 100Mi

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector
nodeSelector:
  apptype: lbs # From original deployment

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: aloa-map-data
    hostPath:
      path: /appdata/webmap_kr_aloa
      # type: DirectoryOrCreate # Type not specified
  - name: lbs-logs
    hostPath:
      path: /logs/aloa-map
      # type: DirectoryOrCreate # Type not specified
volumeMounts:
  - name: aloa-map-data
    mountPath: /appdata/webmap_kr_aloa
  - name: lbs-logs
    mountPath: /logs/debug/map

# ConfigMap data (Converted from original deployment's 'env' section)
configMap:
  enabled: true
  data:
    TZ: "Asia/Seoul"

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Liveness Probe (Not specified in original deployment)
livenessProbe:
  enabled: false

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  kind: lbs-core
  environment: dev
