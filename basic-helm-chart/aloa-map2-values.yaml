# Default values for basic-helm-chart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

appName: aloa-map2

replicaCount: 1

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

image:
  repository: harbor.logisteq.com/lbs-core/dev/aloa-map2
  pullPolicy: Always
  # Specifies the image tag. Defaults to the chart appVersion.
  tag: "latest"

imagePullSecrets:
  - name: harbor-secret

# Optional: Add custom labels to pods
podLabels:
  kind: lbs-core
  environment: dev

# Optional: Add custom annotations to pods
# podAnnotations: {}

# Optional: Pod security context
# podSecurityContext: {}

# Optional: Container security context
# securityContext: {}

service:
  type: NodePort
  ports:
    - name: http # Using 'http' as the name, matching template defaults
      port: 18000
      targetPort: 18000 # Matches the container port exposed by the application
      protocol: TCP
      # nodePort: <Optional: Specify a static NodePort if required>

ingress:
  enabled: true
  # Optional: Specify ingress class name if different from default
  # className: "nginx" # Annotation below already covers this, but className is preferred in newer k8s versions
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: map-tile2-qa.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
          # NOTE: The basic-helm-chart ingress template does NOT support routing to external services like 'amoa-map'.
          # The path '/amoa -> amoa-map:15011' from the original kustomize ingress cannot be directly translated here using this basic chart.
          # You might need to customize the Helm chart's ingress template or manage the 'amoa-map' ingress separately.
    - host: map-tile2-qa.logisteq.com
      paths:
        - path: /
          pathType: Prefix
          # NOTE: Same limitation as above regarding the '/amoa' path routing to 'amoa-map'.
  tls:
    # TLS configuration based on the original ingress.
    # The template expects an array of objects, each with hosts (array) and secretName.
    - hosts:
        - map-tile2-qa.cartamobility.com
      # secretName: <Optional: Specify if a specific secret is needed for this host, otherwise the ingress controller's default certificate might be used>
    - hosts:
        - map-tile2-qa.logisteq.com
      secretName: logisteq-secret-old # Secret containing the TLS certificate for this host

# Optional: Resources limits and requests
resources:
  limits:
    cpu: 1000m
    memory: 5000Mi
  requests:
    cpu: 100m
    memory: 100Mi

# Optional: Node selector
nodeSelector:
  apptype: lbs # Ensures pods are scheduled on nodes with the label 'apptype=lbs'

# Optional: Tolerations
# tolerations: []

# Optional: Affinity
# affinity: {}

# Optional: Volumes and VolumeMounts
# Mapping hostPath volumes from the original deployment. Be cautious using hostPath in production.
volumes:
  - name: aloa-map2-data
    hostPath:
      path: /appdata/webmap_aloa2 # Maps to host's filesystem path
  - name: lbs-logs
    hostPath:
      path: /logs/aloa-map2 # Maps to host's filesystem path
volumeMounts:
  - name: aloa-map2-data
    mountPath: /appdata/webmap_kr_aloa # Mount point inside the container
  - name: lbs-logs
    mountPath: /logs/debug/map # Mount point inside the container

# Optional: Liveness and Readiness Probes (disabled by default in this conversion)
# livenessProbe:
#   enabled: false
#   httpGet:
#     path: / # Adjust path if needed
#     port: http
#   initialDelaySeconds: 15
#   periodSeconds: 20
# readinessProbe:
#   enabled: false
#   httpGet:
#     path: / # Adjust path if needed
#     port: http
#   initialDelaySeconds: 5
#   periodSeconds: 10

# Optional: ConfigMap for environment variables or configuration files
# Mapping the 'env' section from the original deployment using the chart's configMap feature.
configMap:
  enabled: true
  data:
    TZ: Asia/Seoul # Timezone environment variable

# Optional: Lifecycle hooks
# lifecycle: {}

# Optional: Service Account configuration (using chart defaults if not specified)
# serviceAccount:
#   create: true
#   annotations: {}
#   name: "" # Defaults to appName if not set
