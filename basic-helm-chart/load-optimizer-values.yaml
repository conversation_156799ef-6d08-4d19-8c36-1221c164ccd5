# Values for load-optimizer environment, overriding basic-helm-chart defaults

appName: load-optimize # Note: Original resources used 'load-optimize'

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/load-optimizer
  pullPolicy: IfNotPresent
  tag: 20250605-194209

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  type: NodePort
  ports:
    - name: http # Name from original service port definition
      port: 3000
      targetPort: 3000
      protocol: TCP
      nodePort: 30100 # Changed from 30100 to avoid conflicts

# Ingress definition (Disabled as no ingress.yaml was present)
# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: load-optimize.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
    - host: load-optimize.logisteq.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - load-optimize.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host
    - hosts:
        - load-optimize.logisteq.com
      secretName: logisteq-secret-old

# Resource limits and requests
resources:
  limits:
    memory: "512Mi"
    cpu: "500m"
  requests:
    memory: "256Mi"
    cpu: "250m"

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector (Not specified in original deployment)
# nodeSelector: {}

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: load-optimize-claim # Name from original deployment
    hostPath:
      path: /logs/load-optimize
      type: DirectoryOrCreate # Type specified in original
volumeMounts:
  - name: load-optimize-claim # Must match volume name
    mountPath: /logs/load-optimize

# ConfigMap data
configMap:
  enabled: true
  data:
    API_BASE_URL: "http://load-optimize.logisteq.com:15007"
    NODE_ENV: "production"
    ROUTE_URL: "http://lbs-core-dev.logisteq.com:15003"
    OPTIMIZATION_ACCURACY_LEVEL: "5"
    TZ: "Asia/Seoul"

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: /api/liveness # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 3000)
  initialDelaySeconds: 30
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
  timeoutSeconds: 10

# Readiness Probe (Enabled as per original deployment)
readinessProbe:
  enabled: true
  httpGet:
    path: /api/readiness # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 3000)
  initialDelaySeconds: 20
  periodSeconds: 20
  successThreshold: 1
  failureThreshold: 1
  timeoutSeconds: 10
# Additional labels for Pods (None specified in original deployment metadata)
# podLabels: {}
