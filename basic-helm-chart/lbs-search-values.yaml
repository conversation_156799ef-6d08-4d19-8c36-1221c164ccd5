# Values for lbs-search environment, overriding basic-helm-chart defaults

appName: lbs-search

replicaCount: 1

image:
  repository: harbor.logisteq.com/lbs-core/search
  pullPolicy: IfNotPresent
  tag: "latest" # Original deployment used 'latest' tag

imagePullSecrets:
  - name: harbor-secret

# Service definition with multiple ports
service:
  type: NodePort
  ports:
    - name: search-port # Name from original service
      port: 15002
      targetPort: 15002
      protocol: TCP
      nodePort: 31502 # Explicitly set NodePort from original service
    - name: native-port # Name from original service
      port: 15012
      targetPort: 15012
      protocol: TCP # Assuming TCP, not specified in original container ports
      nodePort: 31512 # Explicitly set NodePort from original service

# Ingress definition (Disabled as no ingress.yaml was present)
ingress:
  enabled: false
  # className: "nginx"
  # annotations: {}
  # hosts: []
  # tls: []

# Resource limits and requests
resources:
  limits:
    cpu: 2000m
    memory: 5000Mi
  requests:
    cpu: 300m
    memory: 500Mi

# Node affinity (Not specified in original deployment)
# affinity: {}

# Node selector (Not specified in original deployment)
# nodeSelector: {}

# Volumes and Volume Mounts (Using hostPath from original deployment)
# WARNING: hostPath usage is generally discouraged.
volumes:
  - name: host-log-path
    hostPath:
      path: /logs/search
      # type: DirectoryOrCreate # Type not specified
volumeMounts:
  - name: host-log-path
    mountPath: /logs/search

# ConfigMap data
configMap:
  enabled: true
  data:
    TZ: Asia/Seoul
    SECRET: TMS_Search_Server # TODO: Migrate to Secret

# Deployment strategy (Using Recreate from original deployment)
strategy:
  type: Recreate
  # rollingUpdate: {} # Not applicable for Recreate strategy

# Container lifecycle hooks (None specified in original deployment)
# lifecycle: {}

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: /version # Path from original deployment
    port: http # Template uses named port 'http' (targetPort 15002)
  initialDelaySeconds: 20
  periodSeconds: 10
  # failureThreshold: # Not specified in original

# Readiness Probe (Not specified in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from original metadata)
podLabels:
  kind: lbs-core
  environment: dev
