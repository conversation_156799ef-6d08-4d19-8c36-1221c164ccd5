# Values for carta-admin-web environment, overriding basic-helm-chart defaults

appName: carta-admin-web

replicaCount: 1

image:
  repository: harbor.logisteq.com/carta/admin-frontend
  pullPolicy: IfNotPresent
  tag: "20250213-190209"

imagePullSecrets:
  - name: harbor-secret

# Service definition
service:
  ports: # Add this section to satisfy ingress template
    - port: 80
      targetPort: 80
      type: NodePort

# Ingress definition
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: "nginx" # Kept for clarity
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
  hosts:
    - host: aloa-global.cartamobility.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - aloa-global.cartamobility.com
      # secretName: # Original ingress did not specify a secretName for this host

# Resource limits and requests
resources:
  limits:
    memory: "512Mi"
    cpu: "500m"
  requests:
    memory: "256Mi"
    cpu: "250m"

# Node affinity (Not specified in original deployment)
# affinity: {}

# Volumes and Volume Mounts (Not specified in original deployment)
# volumes: []
# volumeMounts: []

# ConfigMap data (Not present in original Kustomize setup)
configMap:
  enabled: false
  # data: {}

# Deployment strategy
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Liveness Probe (Enabled as per original deployment)
livenessProbe:
  enabled: true
  httpGet:
    path: / # Path from original deployment
    port: http # Template uses named port 'http'
  initialDelaySeconds: 100
  periodSeconds: 60
  successThreshold: 1
  failureThreshold: 3
  timeoutSeconds: 10

# Readiness Probe (Disabled, as not present in original deployment)
readinessProbe:
  enabled: false

# Additional labels for Pods (Taken from Ingress metadata)
podLabels:
  environment: dev
