apiVersion: apps/v1
kind: Deployment
metadata:
  name: himate-web
  labels:
    app: himate-web
spec:
  replicas: 1
  selector:
    matchLabels:
      app: himate-web
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: himate-web
    spec:
      nodeSelector:
        apptype: lbs
      imagePullSecrets:
        - name: harbor-secret
      containers:
        - image: harbor.logisteq.com/himate/himate-web:20250515-112127
          imagePullPolicy: IfNotPresent
          name: himate-web
          resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "128Mi"
              cpu: "100m"
          ports:
            - containerPort: 3000
          livenessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 60
            periodSeconds: 30
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 20
            failureThreshold: 3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: himate-backend
  labels:
    app: himate-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: himate-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: himate-backend
    spec:
      nodeSelector:
        apptype: lbs
      imagePullSecrets:
        - name: harbor-secret
      envFrom:
        - configMapRef:
            name: himate-backend
      containers:
        - image: harbor.logisteq.com/himate/himate-backend:20250602-180628
          name: himate-backend
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              memory: "8000Mi"
              cpu: "500m"
            requests:
              memory: "128Mi"
              cpu: "100m"
          ports:
            - containerPort: 8080
          livenessProbe:
            httpGet:
              path: /monitor/liveness
              port: 8080
            initialDelaySeconds: 120
            periodSeconds: 30
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /monitor/readiness
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 20
            failureThreshold: 3
          volumeMounts:
            - name: himate-log
              mountPath: /applog/himate
      volumes:
        - name: himate-log
          hostPath:
            path: /logs/himate
            type: DirectoryOrCreate

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-chatbot
  labels:
    app: ai-chatbot
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-chatbot
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: ai-chatbot
    spec:
      imagePullSecrets:
        - name: harbor-secret
      nodeSelector:
        chatbot-ai: enable
      containers:
        - image: harbor.logisteq.com/himate/ai-chatbot:20250407-201515
          name: ai-chatbot
          imagePullPolicy: IfNotPresent
          env:
            - name: OPENAI_API_KEY
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: OPENAI_API_KEY
            - name: EMBEDDING_MODEL
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: EMBEDDING_MODEL
            - name: CHAT_MODEL
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: CHAT_MODEL
            - name: CHAT_TEMPERATURE
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: CHAT_TEMPERATURE
            - name: CHUNK_SIZE
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: CHUNK_SIZE
            - name: CHUNK_OVERLAP
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: CHUNK_OVERLAP
            - name: MAX_DOCS_RETRIEVED
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: MAX_DOCS_RETRIEVED
            - name: LANGCHAIN_TRACING_V2
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: LANGCHAIN_TRACING_V2
            - name: LANGSMITH_API_KEY
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: LANGSMITH_API_KEY
            - name: LANGCHAIN_PROJECT
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: LANGCHAIN_PROJECT
            - name: LANGSMITH_ENDPOINT
              valueFrom:
                configMapKeyRef:
                  name: ai-chatbot
                  key: LANGSMITH_ENDPOINT
          resources:
            limits:
              memory: "8000Mi"
              cpu: "4000m"
            requests:
              memory: "256Mi"
              cpu: "300m"
          ports:
            - containerPort: 8000
          volumeMounts:
            - name: chroma-db
              mountPath: /app/chroma_db
      volumes:
        - name: chroma-db
          hostPath:
            path: /app/chroma_db
            type: DirectoryOrCreate
